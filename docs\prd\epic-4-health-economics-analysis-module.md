# 史诗4：卫生经济学分析模块

**史诗目标**：实现全面的卫生经济学评估，包括成本计算、QALY/LYG指标和成本效益分析。此史诗提供政策决策必需的经济分析能力，支持筛查策略的经济学评价。

## 用户故事4.1：成本建模系统

作为**卫生经济学家**，
我希望**建立全面的成本计算模型**，
以便**准确评估筛查策略的经济影响**。

### 验收标准

1. 实现筛查成本配置系统（各种筛查工具的直接成本）
2. 建立治疗成本模型（按癌症分期和治疗方案）
3. 实现间接成本计算（时间成本、交通成本等）
4. 添加成本通胀调整和年度折现率（3%）功能
5. 创建成本参数的敏感性分析工具
6. 实现成本数据的导入和验证功能

## 用户故事4.2：健康结果指标计算

作为**卫生经济学家**，
我希望**计算标准化的健康结果指标**，
以便**量化筛查策略的健康效益**。

### 验收标准

1. 实现质量调整生命年（QALY）计算引擎
2. 计算挽救生命年（LYG）指标
3. 实现健康效用值的年龄和疾病状态调整
4. 添加生命质量权重的配置和管理
5. 创建健康结果的置信区间计算
6. 实现健康结果指标的验证测试

## 用户故事4.3：成本效益分析引擎

作为**政策制定者**，
我希望**进行全面的成本效益分析**，
以便**比较不同筛查策略的经济价值**。

### 验收标准

1. 实现增量成本效益比（ICER）计算
2. 创建成本效益阈值分析功能
3. 实现净健康效益（NHB）计算
4. 添加成本效益可接受曲线（CEAC）生成
5. 创建成本效益分析报告模板
6. 实现多策略成本效益比较功能

## 用户故事4.4：经济分析可视化

作为**决策者**，
我希望**通过直观的图表查看经济分析结果**，
以便**快速理解不同策略的经济影响**。

### 验收标准

1. 创建成本效益散点图和成本效益平面
2. 实现预算影响分析图表
3. 生成敏感性分析的龙卷风图
4. 创建成本分解和效益分解图表
5. 实现交互式经济分析面板
6. 添加经济分析结果的导出功能
