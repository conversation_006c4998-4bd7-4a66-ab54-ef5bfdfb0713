#!/bin/bash

echo "🚀 开始配置阿里云镜像源..."
echo "=================================================="

echo "正在配置pip使用阿里云镜像源..."
pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/
pip config set global.trusted-host mirrors.aliyun.com

if [ $? -eq 0 ]; then
    echo "✅ pip阿里云镜像源配置成功"
else
    echo "❌ pip镜像源配置失败"
fi

echo ""
echo "正在配置Poetry使用阿里云镜像源..."
poetry source add aliyun https://mirrors.aliyun.com/pypi/simple/ --priority=primary

if [ $? -eq 0 ]; then
    echo "✅ Poetry阿里云镜像源配置成功"
else
    echo "❌ Poetry镜像源配置失败"
    echo "注意：pyproject.toml中已包含阿里云源配置"
fi

echo ""
echo "正在验证配置..."
if pip config list | grep -q "mirrors.aliyun.com"; then
    echo "✅ pip阿里云镜像源配置正确"
else
    echo "⚠️  pip镜像源配置可能有问题"
fi

echo ""
echo "=================================================="
echo "🎉 阿里云镜像源配置完成！"
echo ""
echo "提示："
echo "- pip安装包时将自动使用阿里云镜像源"
echo "- Poetry已在pyproject.toml中配置阿里云源"
echo "- 如需恢复默认源，请运行: pip config unset global.index-url"
echo ""
