# Story 2.1: 风险因素管理系统

## Status
Draft

## Story
**As a** 研究人员，
**I want** 定义和管理具有可配置权重的个体风险因素，
**so that** 基于已确立的流行病学因素建模个性化疾病风险。

## Acceptance Criteria
1. 实现风险因素枚举系统（家族史、IBD、肥胖、糖尿病、吸烟、久坐生活方式）
2. 个体风险因素分配，支持布尔值和连续值
3. 风险因素权重配置系统，具有基于文献的默认值
4. 实现综合风险评分计算函数
5. 添加风险因素验证和约束检查
6. 包含风险因素影响文档和参考文献

## Tasks / Subtasks

- [ ] 任务1：实现风险因素枚举和数据结构 (AC: 1)
  - [ ] 创建src/modules/disease/risk_factors.py文件
  - [ ] 定义RiskFactorType枚举（家族史、IBD、肥胖等）
  - [ ] 实现RiskFactor数据类，包含类型、值、权重
  - [ ] 创建RiskFactorProfile类，管理个体风险因素集合
  - [ ] 添加风险因素分类（遗传、生活方式、疾病相关）
  - [ ] 实现风险因素序列化和反序列化功能

- [ ] 任务2：实现个体风险因素分配系统 (AC: 2)
  - [ ] 扩展Individual类，添加risk_factors属性
  - [ ] 实现布尔型风险因素处理（家族史、IBD、吸烟）
  - [ ] 添加连续值风险因素支持（BMI、年龄、久坐时间）
  - [ ] 创建风险因素赋值和更新方法
  - [ ] 实现风险因素历史跟踪功能
  - [ ] 添加风险因素变化时间记录

- [ ] 任务3：创建风险因素权重配置系统 (AC: 3)
  - [ ] 创建data/risk_factor_weights/目录结构
  - [ ] 设计风险因素权重配置文件格式（YAML）
  - [ ] 实现RiskFactorWeights配置管理类
  - [ ] 添加基于文献的默认权重值
  - [ ] 创建权重配置验证和加载功能
  - [ ] 实现动态权重调整和更新机制

- [ ] 任务4：实现综合风险评分计算引擎 (AC: 4)
  - [ ] 创建src/modules/disease/risk_calculator.py文件
  - [ ] 实现RiskCalculator类，计算综合风险评分
  - [ ] 添加加权风险评分算法（线性组合、对数模型）
  - [ ] 实现年龄调整风险评分功能
  - [ ] 创建风险分层功能（低、中、高风险）
  - [ ] 添加风险评分历史跟踪和趋势分析

- [ ] 任务5：添加风险因素验证和约束检查 (AC: 5)
  - [ ] 扩展src/utils/validators.py，添加风险因素验证
  - [ ] 实现BMI范围验证（10-60 kg/m²）
  - [ ] 添加年龄相关约束检查
  - [ ] 创建风险因素组合有效性验证
  - [ ] 实现风险因素数据完整性检查
  - [ ] 添加异常风险因素值检测和警告

- [ ] 任务6：创建风险因素文档和参考文献 (AC: 6)
  - [ ] 创建docs/risk_factors/目录结构
  - [ ] 编写风险因素科学依据文档
  - [ ] 添加权重值来源和参考文献
  - [ ] 创建风险因素使用指南和API文档
  - [ ] 实现风险因素配置示例和模板
  - [ ] 添加风险评分解释和临床意义说明

## Dev Notes

### 风险因素枚举定义
```python
from enum import Enum
from dataclasses import dataclass
from typing import Union, Dict, Any

class RiskFactorType(Enum):
    FAMILY_HISTORY = "family_history"           # 家族史（布尔）
    IBD = "inflammatory_bowel_disease"          # 炎症性肠病（布尔）
    BMI = "body_mass_index"                     # 体重指数（连续）
    DIABETES = "diabetes_mellitus"              # 糖尿病（布尔）
    SMOKING = "smoking_status"                  # 吸烟状态（布尔/分类）
    SEDENTARY_LIFESTYLE = "sedentary_lifestyle" # 久坐生活方式（连续/小时）
    ALCOHOL_CONSUMPTION = "alcohol_consumption" # 酒精消费（连续）
    DIET_QUALITY = "diet_quality"               # 饮食质量（评分）

@dataclass
class RiskFactor:
    factor_type: RiskFactorType
    value: Union[bool, float, int, str]
    weight: float
    last_updated: datetime
    source: str = "default"
```

### 风险因素权重配置格式
```yaml
# data/risk_factor_weights/default_weights.yaml
risk_factor_weights:
  version: "1.0"
  source: "Meta-analysis 2023"
  
  weights:
    family_history:
      value: 2.5
      confidence_interval: [2.1, 3.0]
      reference: "Smith et al. 2023"
    
    inflammatory_bowel_disease:
      value: 4.2
      confidence_interval: [3.5, 5.1]
      reference: "Johnson et al. 2022"
    
    body_mass_index:
      baseline: 25.0  # BMI基线值
      per_unit_increase: 0.05
      max_effect: 1.8
      reference: "Brown et al. 2023"
```

### 综合风险评分算法
```python
def calculate_risk_score(individual: Individual, weights: RiskFactorWeights) -> float:
    """计算个体综合风险评分"""
    base_risk = 1.0
    
    for risk_factor in individual.risk_factors:
        factor_weight = weights.get_weight(risk_factor.factor_type)
        
        if risk_factor.factor_type == RiskFactorType.FAMILY_HISTORY:
            if risk_factor.value:
                base_risk *= factor_weight
        
        elif risk_factor.factor_type == RiskFactorType.BMI:
            bmi_effect = calculate_bmi_effect(risk_factor.value, factor_weight)
            base_risk *= bmi_effect
    
    # 年龄调整
    age_adjusted_risk = apply_age_adjustment(base_risk, individual.age)
    
    return age_adjusted_risk
```

### 风险分层标准
- **低风险**: 风险评分 < 1.5
- **中等风险**: 1.5 ≤ 风险评分 < 3.0
- **高风险**: 风险评分 ≥ 3.0

### 数据验证规则
- BMI范围: 10.0 ≤ BMI ≤ 60.0 kg/m²
- 久坐时间: 0 ≤ 小时 ≤ 24
- 酒精消费: 0 ≤ 单位/周 ≤ 100
- 饮食质量评分: 0 ≤ 评分 ≤ 100
- 布尔型风险因素: True/False值验证

### 风险因素文献依据
- **家族史**: 相对风险 2.5 (95% CI: 2.1-3.0)
- **IBD**: 相对风险 4.2 (95% CI: 3.5-5.1)
- **肥胖**: BMI每增加5单位，风险增加20%
- **糖尿病**: 相对风险 1.8 (95% CI: 1.5-2.2)
- **吸烟**: 相对风险 2.0 (95% CI: 1.7-2.4)

### Testing
#### 测试文件位置
- `tests/unit/test_risk_factors.py`
- `tests/unit/test_risk_calculator.py`
- `tests/integration/test_risk_factor_integration.py`

#### 测试标准
- 风险因素枚举和数据结构测试
- 个体风险因素分配和更新测试
- 权重配置加载和验证测试
- 综合风险评分计算准确性测试
- 数据验证和约束检查测试

#### 测试框架和模式
- 使用pytest参数化测试不同风险因素组合
- Mock配置文件测试权重加载
- 数值精度测试验证计算准确性
- 边界值测试验证约束检查

#### 特定测试要求
- 风险评分计算精度: 误差 < 0.1%
- 配置加载性能: < 100ms
- 内存使用: 10万个体风险因素 < 500MB
- 数据完整性: 所有风险因素都有有效权重

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-31 | 1.0 | 初始故事创建 | Scrum Master |

## Dev Agent Record
*此部分将由开发代理在实施过程中填写*

### Agent Model Used
*待填写*

### Debug Log References
*待填写*

### Completion Notes List
*待填写*

### File List
*待填写*

## QA Results
*此部分将由QA代理在完成故事实施的QA审查后填写*
