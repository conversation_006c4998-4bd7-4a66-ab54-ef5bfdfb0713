# <类型>(<范围>): <主题>
#
# <正文>
#
# <页脚>

# 类型说明:
# feat:     新功能
# fix:      修复bug
# docs:     文档更新
# style:    代码格式化，不影响功能
# refactor: 重构代码
# test:     添加或修改测试
# chore:    构建过程或辅助工具的变动
# perf:     性能优化
# ci:       CI/CD相关更改

# 范围说明:
# core:     核心模拟引擎
# modules:  功能模块
# ui:       用户界面
# db:       数据库相关
# tests:    测试相关
# docs:     文档相关
# config:   配置相关

# 示例:
# feat(core): 添加人口初始化模块
# 
# - 实现基于年龄和性别的人口分布
# - 支持自定义人口规模参数
# - 添加人口统计数据验证
#
# Closes #123
