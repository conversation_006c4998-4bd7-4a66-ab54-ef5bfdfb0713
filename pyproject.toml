[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "colorectal-cancer-screening-model"
version = "0.1.0"
description = "结直肠癌筛查微观模拟模型 - 用于评估不同筛查策略的成本效益分析"
authors = ["Development Team <<EMAIL>>"]
readme = "README.md"
packages = [{include = "src"}]

[[tool.poetry.source]]
name = "aliyun"
url = "https://mirrors.aliyun.com/pypi/simple/"
priority = "primary"

[[tool.poetry.source]]
name = "PyPI"
priority = "supplemental"

[tool.poetry.dependencies]
python = "^3.9"
numpy = "^1.24.0"
scipy = "^1.10.0"
pandas = "^2.0.0"
PyQt6 = "^6.5.0"
matplotlib = "^3.7.0"
plotly = "^5.15.0"
SQLAlchemy = "^2.0.0"
PyYAML = "^6.0.0"
tensorflow = "^2.13.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-cov = "^4.1.0"
pytest-qt = "^4.2.0"
black = "^23.0.0"
isort = "^5.12.0"
mypy = "^1.5.0"
pre-commit = "^3.3.0"
sphinx = "^7.0.0"
pyinstaller = "^5.13.0"

[tool.poetry.group.test.dependencies]
pytest-benchmark = "^4.0.0"
pytest-mock = "^3.11.0"
factory-boy = "^3.3.0"

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "matplotlib.*",
    "plotly.*",
    "tensorflow.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.4"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
markers = [
    "unit: 单元测试标记",
    "integration: 集成测试标记",
    "performance: 性能测试标记",
    "slow: 慢速测试标记",
]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
precision = 2

[tool.coverage.html]
directory = "htmlcov"
