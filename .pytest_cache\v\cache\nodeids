["tests/integration/test_core_data_structures.py::TestComplexScenarios::test_screening_intervention_scenario", "tests/integration/test_core_data_structures.py::TestCoreDataStructuresIntegration::test_age_progression_simulation", "tests/integration/test_core_data_structures.py::TestCoreDataStructuresIntegration::test_complete_disease_progression_simulation", "tests/integration/test_core_data_structures.py::TestCoreDataStructuresIntegration::test_individual_population_integration", "tests/integration/test_core_data_structures.py::TestCoreDataStructuresIntegration::test_population_statistics_with_disease_progression", "tests/integration/test_core_data_structures.py::TestCoreDataStructuresIntegration::test_simulation_population_integration", "tests/integration/test_core_data_structures.py::TestCoreDataStructuresIntegration::test_simulation_state_persistence", "tests/integration/test_core_data_structures.py::TestCoreDataStructuresIntegration::test_validation_integration", "tests/unit/test_individual.py::TestHealthEvent::test_health_event_creation", "tests/unit/test_individual.py::TestHealthEvent::test_health_event_invalid_age", "tests/unit/test_individual.py::TestHealthEvent::test_health_event_invalid_state", "tests/unit/test_individual.py::TestIndividual::test_get_current_age", "tests/unit/test_individual.py::TestIndividual::test_get_health_history_by_type", "tests/unit/test_individual.py::TestIndividual::test_get_time_in_state", "tests/unit/test_individual.py::TestIndividual::test_individual_creation_basic", "tests/unit/test_individual.py::TestIndividual::test_individual_creation_with_custom_id", "tests/unit/test_individual.py::TestIndividual::test_individual_creation_with_pathway", "tests/unit/test_individual.py::TestIndividual::test_individual_incompatible_pathway_state", "tests/unit/test_individual.py::TestIndividual::test_individual_invalid_birth_year", "tests/unit/test_individual.py::TestIndividual::test_individual_invalid_gender", "tests/unit/test_individual.py::TestIndividual::test_is_alive", "tests/unit/test_individual.py::TestIndividual::test_repr", "tests/unit/test_individual.py::TestIndividual::test_state_transition_cancer_without_stage", "tests/unit/test_individual.py::TestIndividual::test_state_transition_from_death", "tests/unit/test_individual.py::TestIndividual::test_state_transition_invalid", "tests/unit/test_individual.py::TestIndividual::test_state_transition_non_cancer_with_stage", "tests/unit/test_individual.py::TestIndividual::test_state_transition_to_cancer_with_stage", "tests/unit/test_individual.py::TestIndividual::test_state_transition_valid", "tests/unit/test_individual.py::TestIndividual::test_to_dict", "tests/unit/test_individual.py::TestIndividualIntegration::test_complete_disease_progression", "tests/unit/test_individual.py::TestIndividualIntegration::test_treatment_recovery", "tests/unit/test_infrastructure_setup.py::TestInfrastructureSetup::test_code_quality_tools_configuration", "tests/unit/test_infrastructure_setup.py::TestInfrastructureSetup::test_development_dependencies", "tests/unit/test_infrastructure_setup.py::TestInfrastructureSetup::test_docker_configuration_files", "tests/unit/test_infrastructure_setup.py::TestInfrastructureSetup::test_git_configuration_files", "tests/unit/test_infrastructure_setup.py::TestInfrastructureSetup::test_github_workflows", "tests/unit/test_infrastructure_setup.py::TestInfrastructureSetup::test_pyproject_toml_configuration", "tests/unit/test_infrastructure_setup.py::TestInfrastructureSetup::test_readme_exists", "tests/unit/test_mirror_configuration.py::TestMirrorConfiguration::test_ci_workflow_mirror_configuration", "tests/unit/test_mirror_configuration.py::TestMirrorConfiguration::test_dockerfile_mirror_configuration", "tests/unit/test_mirror_configuration.py::TestMirrorConfiguration::test_pip_conf_exists", "tests/unit/test_mirror_configuration.py::TestMirrorConfiguration::test_poetry_aliyun_source_configured", "tests/unit/test_mirror_configuration.py::TestMirrorConfiguration::test_readme_mirror_instructions", "tests/unit/test_mirror_configuration.py::TestMirrorConfiguration::test_setup_scripts_exist", "tests/unit/test_population.py::TestPopulation::test_add_duplicate_individual", "tests/unit/test_population.py::TestPopulation::test_add_individual", "tests/unit/test_population.py::TestPopulation::test_batch_transition_states", "tests/unit/test_population.py::TestPopulation::test_clear", "tests/unit/test_population.py::TestPopulation::test_contains", "tests/unit/test_population.py::TestPopulation::test_filter_by_age_range", "tests/unit/test_population.py::TestPopulation::test_filter_by_criteria", "tests/unit/test_population.py::TestPopulation::test_filter_by_disease_state", "tests/unit/test_population.py::TestPopulation::test_filter_by_gender", "tests/unit/test_population.py::TestPopulation::test_get_alive_individuals", "tests/unit/test_population.py::TestPopulation::test_get_cancer_patients", "tests/unit/test_population.py::TestPopulation::test_get_individual", "tests/unit/test_population.py::TestPopulation::test_iteration", "tests/unit/test_population.py::TestPopulation::test_population_creation_empty", "tests/unit/test_population.py::TestPopulation::test_population_creation_with_individuals", "tests/unit/test_population.py::TestPopulation::test_remove_individual", "tests/unit/test_population.py::TestPopulation::test_remove_nonexistent_individual", "tests/unit/test_population.py::TestPopulation::test_repr", "tests/unit/test_population.py::TestPopulation::test_to_dict", "tests/unit/test_population.py::TestPopulationStatistics::test_age_distribution_custom_bins", "tests/unit/test_population.py::TestPopulationStatistics::test_age_distribution_default_bins", "tests/unit/test_population.py::TestPopulationStatistics::test_cancer_stage_distribution", "tests/unit/test_population.py::TestPopulationStatistics::test_disease_state_distribution", "tests/unit/test_population.py::TestPopulationStatistics::test_gender_distribution", "tests/unit/test_population.py::TestPopulationStatistics::test_mean_age", "tests/unit/test_population.py::TestPopulationStatistics::test_summary", "tests/unit/test_population.py::TestPopulationStatistics::test_survival_rate", "tests/unit/test_project_structure.py::TestProjectStructure::test_core_modules_exist", "tests/unit/test_project_structure.py::TestProjectStructure::test_pyproject_toml_exists", "tests/unit/test_project_structure.py::TestProjectStructure::test_src_directory_exists", "tests/unit/test_project_structure.py::TestProjectStructure::test_src_import", "tests/unit/test_project_structure.py::TestProjectStructure::test_tests_directory_structure", "tests/unit/test_simulation.py::TestEventQueue::test_add_and_get_event", "tests/unit/test_simulation.py::TestEventQueue::test_clear_queue", "tests/unit/test_simulation.py::TestEventQueue::test_event_ordering_by_priority", "tests/unit/test_simulation.py::TestEventQueue::test_event_ordering_by_time", "tests/unit/test_simulation.py::TestEventQueue::test_event_queue_creation", "tests/unit/test_simulation.py::TestEventQueue::test_get_events_at_time", "tests/unit/test_simulation.py::TestEventQueue::test_peek_next_event", "tests/unit/test_simulation.py::TestEventQueue::test_remove_events_for_individual", "tests/unit/test_simulation.py::TestSimulationEvent::test_simulation_event_creation", "tests/unit/test_simulation.py::TestSimulationEvent::test_simulation_event_empty_type", "tests/unit/test_simulation.py::TestSimulationEvent::test_simulation_event_invalid_time", "tests/unit/test_simulation.py::TestSimulationEvent::test_simulation_event_with_data", "tests/unit/test_simulation.py::TestSimulationIntegration::test_complete_simulation_workflow", "tests/unit/test_simulation.py::TestSimulationState::test_event_processing", "tests/unit/test_simulation.py::TestSimulationState::test_event_scheduling", "tests/unit/test_simulation.py::TestSimulationState::test_parameter_management", "tests/unit/test_simulation.py::TestSimulationState::test_progress_tracking", "tests/unit/test_simulation.py::TestSimulationState::test_repr", "tests/unit/test_simulation.py::TestSimulationState::test_result_management", "tests/unit/test_simulation.py::TestSimulationState::test_simulation_lifecycle", "tests/unit/test_simulation.py::TestSimulationState::test_simulation_lifecycle_errors", "tests/unit/test_simulation.py::TestSimulationState::test_simulation_state_creation", "tests/unit/test_simulation.py::TestSimulationState::test_simulation_state_invalid_parameters", "tests/unit/test_simulation.py::TestSimulationState::test_state_serialization", "tests/unit/test_simulation.py::TestSimulationState::test_time_advancement", "tests/unit/test_simulation.py::TestSimulationState::test_time_advancement_errors", "tests/unit/test_simulation.py::TestSimulationState::test_to_dict", "tests/unit/test_validators.py::TestAgeValidation::test_validate_age_invalid_type", "tests/unit/test_validators.py::TestAgeValidation::test_validate_age_negative", "tests/unit/test_validators.py::TestAgeValidation::test_validate_age_too_old", "tests/unit/test_validators.py::TestAgeValidation::test_validate_age_valid", "tests/unit/test_validators.py::TestBirthYearValidation::test_validate_birth_year_invalid_type", "tests/unit/test_validators.py::TestBirthYearValidation::test_validate_birth_year_too_early", "tests/unit/test_validators.py::TestBirthYearValidation::test_validate_birth_year_too_late", "tests/unit/test_validators.py::TestBirthYearValidation::test_validate_birth_year_valid", "tests/unit/test_validators.py::TestCancerStageValidation::test_validate_cancer_stage_enum", "tests/unit/test_validators.py::TestCancerStageValidation::test_validate_cancer_stage_invalid", "tests/unit/test_validators.py::TestCancerStageValidation::test_validate_cancer_stage_none", "tests/unit/test_validators.py::TestCancerStageValidation::test_validate_cancer_stage_string", "tests/unit/test_validators.py::TestDiseaseStateValidation::test_validate_disease_state_enum", "tests/unit/test_validators.py::TestDiseaseStateValidation::test_validate_disease_state_invalid", "tests/unit/test_validators.py::TestDiseaseStateValidation::test_validate_disease_state_string", "tests/unit/test_validators.py::TestEnumValueValidation::test_validate_enum_value_enum", "tests/unit/test_validators.py::TestEnumValueValidation::test_validate_enum_value_invalid_string", "tests/unit/test_validators.py::TestEnumValueValidation::test_validate_enum_value_invalid_type", "tests/unit/test_validators.py::TestEnumValueValidation::test_validate_enum_value_string", "tests/unit/test_validators.py::TestGenderValidation::test_validate_gender_enum", "tests/unit/test_validators.py::TestGenderValidation::test_validate_gender_invalid_string", "tests/unit/test_validators.py::TestGenderValidation::test_validate_gender_invalid_type", "tests/unit/test_validators.py::TestGenderValidation::test_validate_gender_string", "tests/unit/test_validators.py::TestIndividualIdValidation::test_validate_individual_id_empty", "tests/unit/test_validators.py::TestIndividualIdValidation::test_validate_individual_id_invalid_type", "tests/unit/test_validators.py::TestIndividualIdValidation::test_validate_individual_id_too_short", "tests/unit/test_validators.py::TestIndividualIdValidation::test_validate_individual_id_valid", "tests/unit/test_validators.py::TestIndividualIdValidation::test_validate_individual_id_with_whitespace", "tests/unit/test_validators.py::TestPathwayTypeValidation::test_validate_pathway_type_enum", "tests/unit/test_validators.py::TestPathwayTypeValidation::test_validate_pathway_type_invalid", "tests/unit/test_validators.py::TestPathwayTypeValidation::test_validate_pathway_type_none", "tests/unit/test_validators.py::TestPathwayTypeValidation::test_validate_pathway_type_string", "tests/unit/test_validators.py::TestPositiveNumberValidation::test_validate_positive_number_invalid_type", "tests/unit/test_validators.py::TestPositiveNumberValidation::test_validate_positive_number_negative", "tests/unit/test_validators.py::TestPositiveNumberValidation::test_validate_positive_number_valid", "tests/unit/test_validators.py::TestPositiveNumberValidation::test_validate_positive_number_with_zero", "tests/unit/test_validators.py::TestProbabilityValidation::test_validate_probability_invalid_type", "tests/unit/test_validators.py::TestProbabilityValidation::test_validate_probability_out_of_range", "tests/unit/test_validators.py::TestProbabilityValidation::test_validate_probability_valid", "tests/unit/test_validators.py::TestStateTransitionValidation::test_validate_state_transition_from_death", "tests/unit/test_validators.py::TestStateTransitionValidation::test_validate_state_transition_invalid", "tests/unit/test_validators.py::TestStateTransitionValidation::test_validate_state_transition_pathway_incompatible", "tests/unit/test_validators.py::TestStateTransitionValidation::test_validate_state_transition_valid", "tests/unit/test_validators.py::TestValidationExceptions::test_specific_validation_errors", "tests/unit/test_validators.py::TestValidationExceptions::test_validation_error_basic", "tests/unit/test_validators.py::TestValidationExceptions::test_validation_error_with_field"]