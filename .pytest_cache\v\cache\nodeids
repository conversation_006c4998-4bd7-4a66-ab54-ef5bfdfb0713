["tests/unit/test_infrastructure_setup.py::TestInfrastructureSetup::test_code_quality_tools_configuration", "tests/unit/test_infrastructure_setup.py::TestInfrastructureSetup::test_development_dependencies", "tests/unit/test_infrastructure_setup.py::TestInfrastructureSetup::test_docker_configuration_files", "tests/unit/test_infrastructure_setup.py::TestInfrastructureSetup::test_git_configuration_files", "tests/unit/test_infrastructure_setup.py::TestInfrastructureSetup::test_github_workflows", "tests/unit/test_infrastructure_setup.py::TestInfrastructureSetup::test_pyproject_toml_configuration", "tests/unit/test_infrastructure_setup.py::TestInfrastructureSetup::test_readme_exists", "tests/unit/test_mirror_configuration.py::TestMirrorConfiguration::test_ci_workflow_mirror_configuration", "tests/unit/test_mirror_configuration.py::TestMirrorConfiguration::test_dockerfile_mirror_configuration", "tests/unit/test_mirror_configuration.py::TestMirrorConfiguration::test_pip_conf_exists", "tests/unit/test_mirror_configuration.py::TestMirrorConfiguration::test_poetry_aliyun_source_configured", "tests/unit/test_mirror_configuration.py::TestMirrorConfiguration::test_readme_mirror_instructions", "tests/unit/test_mirror_configuration.py::TestMirrorConfiguration::test_setup_scripts_exist", "tests/unit/test_project_structure.py::TestProjectStructure::test_core_modules_exist", "tests/unit/test_project_structure.py::TestProjectStructure::test_pyproject_toml_exists", "tests/unit/test_project_structure.py::TestProjectStructure::test_src_directory_exists", "tests/unit/test_project_structure.py::TestProjectStructure::test_src_import", "tests/unit/test_project_structure.py::TestProjectStructure::test_tests_directory_structure"]