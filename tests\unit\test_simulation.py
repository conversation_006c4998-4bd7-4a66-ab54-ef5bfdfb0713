"""
模拟状态管理系统单元测试

测试SimulationState类的状态管理、时间跟踪、参数存储和事件调度功能。
"""

import pytest
import json
import tempfile
from pathlib import Path
from unittest.mock import patch, MagicMock

from src.core.simulation import SimulationState, SimulationEvent, EventQueue


class TestSimulationEvent:
    """测试SimulationEvent类"""
    
    def test_simulation_event_creation(self):
        """测试模拟事件创建"""
        event = SimulationEvent(
            event_id="test-event-001",
            event_time=5.5,
            event_type="screening",
            target_individual_id="individual-123",
            priority=1
        )
        
        assert event.event_id == "test-event-001"
        assert event.event_time == 5.5
        assert event.event_type == "screening"
        assert event.target_individual_id == "individual-123"
        assert event.priority == 1
        assert event.event_data == {}
    
    def test_simulation_event_with_data(self):
        """测试带额外数据的模拟事件"""
        event_data = {"screening_type": "colonoscopy", "result": "positive"}
        event = SimulationEvent(
            event_id="test-event-002",
            event_time=10.0,
            event_type="screening",
            target_individual_id=None,
            event_data=event_data
        )
        
        assert event.event_data == event_data
    
    def test_simulation_event_invalid_time(self):
        """测试无效事件时间"""
        with pytest.raises(ValueError, match="事件时间不能为负数"):
            SimulationEvent(
                event_id="test-event-003",
                event_time=-1.0,
                event_type="test",
                target_individual_id=None
            )
    
    def test_simulation_event_empty_type(self):
        """测试空事件类型"""
        with pytest.raises(ValueError, match="事件类型不能为空"):
            SimulationEvent(
                event_id="test-event-004",
                event_time=1.0,
                event_type="",
                target_individual_id=None
            )


class TestEventQueue:
    """测试EventQueue类"""
    
    def test_event_queue_creation(self):
        """测试事件队列创建"""
        queue = EventQueue()
        
        assert queue.size() == 0
        assert queue.is_empty() is True
    
    def test_add_and_get_event(self):
        """测试添加和获取事件"""
        queue = EventQueue()
        
        event = SimulationEvent(
            event_id="test-001",
            event_time=5.0,
            event_type="test",
            target_individual_id=None
        )
        
        queue.add_event(event)
        
        assert queue.size() == 1
        assert queue.is_empty() is False
        
        retrieved_event = queue.get_next_event()
        assert retrieved_event == event
        assert queue.size() == 0
    
    def test_event_ordering_by_time(self):
        """测试事件按时间排序"""
        queue = EventQueue()
        
        # 添加不同时间的事件
        event1 = SimulationEvent("e1", 10.0, "test", None)
        event2 = SimulationEvent("e2", 5.0, "test", None)
        event3 = SimulationEvent("e3", 15.0, "test", None)
        
        queue.add_event(event1)
        queue.add_event(event2)
        queue.add_event(event3)
        
        # 验证按时间顺序获取
        assert queue.get_next_event().event_time == 5.0
        assert queue.get_next_event().event_time == 10.0
        assert queue.get_next_event().event_time == 15.0
    
    def test_event_ordering_by_priority(self):
        """测试相同时间事件按优先级排序"""
        queue = EventQueue()
        
        # 添加相同时间但不同优先级的事件
        event1 = SimulationEvent("e1", 5.0, "test", None, priority=2)
        event2 = SimulationEvent("e2", 5.0, "test", None, priority=1)
        event3 = SimulationEvent("e3", 5.0, "test", None, priority=3)
        
        queue.add_event(event1)
        queue.add_event(event2)
        queue.add_event(event3)
        
        # 验证按优先级顺序获取（数字越小优先级越高）
        assert queue.get_next_event().priority == 1
        assert queue.get_next_event().priority == 2
        assert queue.get_next_event().priority == 3
    
    def test_peek_next_event(self):
        """测试查看下一个事件"""
        queue = EventQueue()
        
        event = SimulationEvent("test", 5.0, "test", None)
        queue.add_event(event)
        
        # 查看事件但不移除
        peeked_event = queue.peek_next_event()
        assert peeked_event == event
        assert queue.size() == 1
        
        # 获取事件并移除
        retrieved_event = queue.get_next_event()
        assert retrieved_event == event
        assert queue.size() == 0
    
    def test_get_events_at_time(self):
        """测试获取指定时间的事件"""
        queue = EventQueue()
        
        # 添加不同时间的事件
        event1 = SimulationEvent("e1", 5.0, "test", None)
        event2 = SimulationEvent("e2", 5.0, "test", None)
        event3 = SimulationEvent("e3", 10.0, "test", None)
        
        queue.add_event(event1)
        queue.add_event(event2)
        queue.add_event(event3)
        
        events_at_5 = queue.get_events_at_time(5.0)
        assert len(events_at_5) == 2
        
        events_at_10 = queue.get_events_at_time(10.0)
        assert len(events_at_10) == 1
    
    def test_remove_events_for_individual(self):
        """测试移除指定个体的事件"""
        queue = EventQueue()
        
        # 添加不同个体的事件
        event1 = SimulationEvent("e1", 5.0, "test", "individual-1")
        event2 = SimulationEvent("e2", 10.0, "test", "individual-2")
        event3 = SimulationEvent("e3", 15.0, "test", "individual-1")
        
        queue.add_event(event1)
        queue.add_event(event2)
        queue.add_event(event3)
        
        # 移除individual-1的事件
        removed_count = queue.remove_events_for_individual("individual-1")
        
        assert removed_count == 2
        assert queue.size() == 1
        
        # 验证剩余事件
        remaining_event = queue.get_next_event()
        assert remaining_event.target_individual_id == "individual-2"
    
    def test_clear_queue(self):
        """测试清空队列"""
        queue = EventQueue()
        
        # 添加一些事件
        for i in range(5):
            event = SimulationEvent(f"e{i}", float(i), "test", None)
            queue.add_event(event)
        
        assert queue.size() == 5
        
        queue.clear()
        
        assert queue.size() == 0
        assert queue.is_empty() is True


class TestSimulationState:
    """测试SimulationState类"""
    
    def test_simulation_state_creation(self):
        """测试模拟状态创建"""
        simulation = SimulationState(
            start_year=2025,
            duration_years=50,
            time_step=1.0,
            random_seed=42
        )
        
        assert simulation.start_year == 2025
        assert simulation.duration_years == 50
        assert simulation.time_step == 1.0
        assert simulation.random_seed == 42
        assert simulation.current_time == 0.0
        assert simulation.current_year == 2025
        assert simulation.is_running is False
        assert simulation.is_completed is False
        assert simulation.simulation_id is not None
    
    def test_simulation_state_invalid_parameters(self):
        """测试无效的模拟参数"""
        with pytest.raises(ValueError, match="模拟持续时间必须大于0"):
            SimulationState(duration_years=0)
        
        with pytest.raises(ValueError, match="时间步长必须大于0"):
            SimulationState(time_step=0)
        
        with pytest.raises(ValueError, match="开始年份不能早于1900年"):
            SimulationState(start_year=1800)
    
    def test_parameter_management(self):
        """测试参数管理"""
        simulation = SimulationState()
        
        # 设置参数
        simulation.set_parameter("population_size", 10000)
        simulation.set_parameter("screening_rate", 0.7)
        
        # 获取参数
        assert simulation.get_parameter("population_size") == 10000
        assert simulation.get_parameter("screening_rate") == 0.7
        assert simulation.get_parameter("nonexistent", "default") == "default"
    
    def test_result_management(self):
        """测试结果管理"""
        simulation = SimulationState()
        
        # 设置结果
        simulation.set_result("total_cancers", 150)
        simulation.set_result("survival_rate", 0.85)
        
        # 获取结果
        assert simulation.get_result("total_cancers") == 150
        assert simulation.get_result("survival_rate") == 0.85
        assert simulation.get_result("nonexistent", 0) == 0
    
    def test_simulation_lifecycle(self):
        """测试模拟生命周期"""
        simulation = SimulationState(duration_years=10)
        
        # 初始状态
        assert simulation.is_running is False
        assert simulation.is_paused is False
        assert simulation.is_completed is False
        
        # 开始模拟
        simulation.start_simulation()
        assert simulation.is_running is True
        assert simulation.start_time is not None
        
        # 暂停模拟
        simulation.pause_simulation()
        assert simulation.is_paused is True
        
        # 恢复模拟
        simulation.resume_simulation()
        assert simulation.is_paused is False
        
        # 停止模拟
        simulation.stop_simulation()
        assert simulation.is_running is False
        assert simulation.is_completed is True
        assert simulation.end_time is not None
    
    def test_simulation_lifecycle_errors(self):
        """测试模拟生命周期错误处理"""
        simulation = SimulationState()
        
        # 尝试暂停未运行的模拟
        with pytest.raises(RuntimeError, match="模拟未在运行"):
            simulation.pause_simulation()
        
        # 尝试恢复未暂停的模拟
        with pytest.raises(RuntimeError, match="模拟未暂停"):
            simulation.resume_simulation()
        
        # 尝试重复开始模拟
        simulation.start_simulation()
        with pytest.raises(RuntimeError, match="模拟已在运行中"):
            simulation.start_simulation()
    
    def test_time_advancement(self):
        """测试时间推进"""
        simulation = SimulationState(
            start_year=2025,
            duration_years=10,
            time_step=1.0
        )
        
        simulation.start_simulation()
        
        # 推进时间
        simulation.advance_time()
        assert simulation.current_time == 1.0
        assert simulation.current_year == 2026
        
        # 自定义时间增量
        simulation.advance_time(2.5)
        assert simulation.current_time == 3.5
        assert simulation.current_year == 2028
        
        # 推进到结束
        simulation.advance_time(6.5)
        assert simulation.current_time == 10.0
        assert simulation.is_completed is True
    
    def test_time_advancement_errors(self):
        """测试时间推进错误处理"""
        simulation = SimulationState()
        
        # 尝试在未运行时推进时间
        with pytest.raises(RuntimeError, match="模拟未运行或已暂停"):
            simulation.advance_time()
        
        # 尝试在暂停时推进时间
        simulation.start_simulation()
        simulation.pause_simulation()
        with pytest.raises(RuntimeError, match="模拟未运行或已暂停"):
            simulation.advance_time()
    
    def test_event_scheduling(self):
        """测试事件调度"""
        simulation = SimulationState()
        simulation.start_simulation()
        
        # 调度事件
        event_id = simulation.schedule_event(
            event_type="screening",
            delay=5.0,
            target_individual_id="individual-123",
            event_data={"type": "colonoscopy"},
            priority=1
        )
        
        assert event_id is not None
        assert simulation.event_queue.size() == 1
        
        # 验证事件内容
        event = simulation.event_queue.peek_next_event()
        assert event.event_type == "screening"
        assert event.event_time == 5.0
        assert event.target_individual_id == "individual-123"
        assert event.event_data["type"] == "colonoscopy"
    
    def test_event_processing(self):
        """测试事件处理"""
        simulation = SimulationState()
        simulation.start_simulation()
        
        # 注册事件处理器
        handler_called = False
        def test_handler(event, sim_state):
            nonlocal handler_called
            handler_called = True
            assert event.event_type == "test_event"
            assert sim_state == simulation
        
        simulation.register_event_handler("test_event", test_handler)
        
        # 调度和处理事件
        simulation.schedule_event("test_event", 2.0)
        processed_event = simulation.process_next_event()
        
        assert processed_event is not None
        assert processed_event.event_type == "test_event"
        assert simulation.current_time == 2.0
        assert simulation.total_events_processed == 1
        assert handler_called is True
    
    def test_progress_tracking(self):
        """测试进度跟踪"""
        simulation = SimulationState(duration_years=10)
        simulation.start_simulation()
        
        # 初始进度
        assert simulation.get_progress() == 0.0
        assert simulation.get_remaining_time() == 10.0
        
        # 推进时间
        simulation.advance_time(3.0)
        assert simulation.get_progress() == 0.3
        assert simulation.get_remaining_time() == 7.0
        
        # 完成模拟
        simulation.advance_time(7.0)
        assert simulation.get_progress() == 1.0
        assert simulation.get_remaining_time() == 0.0
    
    def test_state_serialization(self):
        """测试状态序列化和反序列化"""
        simulation = SimulationState(
            start_year=2025,
            duration_years=50,
            random_seed=42
        )
        
        # 设置一些参数和结果
        simulation.set_parameter("population_size", 10000)
        simulation.set_result("total_cancers", 150)
        
        # 调度一些事件
        simulation.start_simulation()
        simulation.schedule_event("screening", 5.0, "individual-1")
        simulation.advance_time(2.0)
        
        # 保存状态
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_path = f.name
        
        try:
            simulation.save_state(temp_path)
            
            # 加载状态
            loaded_simulation = SimulationState.load_state(temp_path)
            
            # 验证加载的状态
            assert loaded_simulation.start_year == simulation.start_year
            assert loaded_simulation.duration_years == simulation.duration_years
            assert loaded_simulation.random_seed == simulation.random_seed
            assert loaded_simulation.current_time == simulation.current_time
            assert loaded_simulation.get_parameter("population_size") == 10000
            assert loaded_simulation.get_result("total_cancers") == 150
            assert loaded_simulation.event_queue.size() == 1
            
        finally:
            Path(temp_path).unlink()
    
    def test_to_dict(self):
        """测试转换为字典"""
        simulation = SimulationState(
            start_year=2025,
            duration_years=50
        )
        
        simulation.start_simulation()
        simulation.advance_time(10.0)
        simulation.schedule_event("test", 5.0)
        
        data = simulation.to_dict()
        
        # 验证字典内容
        assert data["current_time"] == 10.0
        assert data["current_year"] == 2035
        assert data["progress"] == 0.2
        assert data["is_running"] is True
        assert data["events_in_queue"] == 1
    
    def test_repr(self):
        """测试字符串表示"""
        simulation = SimulationState(duration_years=50)
        simulation.start_simulation()
        simulation.advance_time(10.0)
        
        repr_str = repr(simulation)
        assert "SimulationState" in repr_str
        assert "10.0/50" in repr_str
        assert "20.0%" in repr_str


@pytest.fixture
def sample_simulation():
    """创建示例模拟状态用于测试"""
    simulation = SimulationState(
        start_year=2025,
        duration_years=20,
        time_step=1.0,
        random_seed=42
    )
    simulation.start_simulation()
    return simulation


class TestSimulationIntegration:
    """模拟状态集成测试"""
    
    def test_complete_simulation_workflow(self, sample_simulation):
        """测试完整的模拟工作流"""
        simulation = sample_simulation
        
        # 设置参数
        simulation.set_parameter("population_size", 1000)
        simulation.set_parameter("screening_interval", 2)
        
        # 调度多个事件
        for i in range(5):
            simulation.schedule_event(
                "screening",
                delay=float(i * 2),
                target_individual_id=f"individual-{i}"
            )
        
        # 处理事件直到队列为空
        events_processed = 0
        while not simulation.event_queue.is_empty():
            event = simulation.process_next_event()
            if event:
                events_processed += 1
                # 记录结果
                simulation.set_result(f"event_{events_processed}", event.event_type)
        
        # 验证结果
        assert events_processed == 5
        assert simulation.current_time == 8.0  # 最后一个事件的时间
        assert simulation.get_result("event_1") == "screening"
        
        # 完成模拟
        simulation.advance_time(12.0)  # 推进到结束
        assert simulation.is_completed is True
