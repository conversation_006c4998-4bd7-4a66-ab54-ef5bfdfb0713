# 系统集成架构

## 服务层架构

### 核心服务接口

```python
class SimulationService:
    """模拟服务层"""
    
    def __init__(self):
        self.engine = MicrosimulationEngine()
        self.database = DatabaseManager()
        self.cache = CacheManager()
        
    async def create_simulation(self, config: SimulationConfig) -> str:
        """创建新的模拟任务"""
        simulation_id = str(uuid.uuid4())
        
        # 保存配置到数据库
        await self.database.save_simulation_config(simulation_id, config)
        
        return simulation_id
        
    async def run_simulation(self, simulation_id: str) -> SimulationResults:
        """执行模拟任务"""
        config = await self.database.load_simulation_config(simulation_id)
        
        # 在后台线程中运行模拟
        results = await self.run_simulation_async(config)
        
        # 保存结果
        await self.database.save_simulation_results(simulation_id, results)
        
        return results

class DataService:
    """数据服务层"""
    
    def __init__(self):
        self.database = DatabaseManager()
        self.file_manager = FileManager()
        
    async def import_life_table(self, file_path: str, metadata: Dict) -> str:
        """导入生命表数据"""
        data = await self.file_manager.read_csv(file_path)
        life_table_id = await self.database.save_life_table(data, metadata)
        return life_table_id
        
    async def import_calibration_targets(self, targets: List[CalibrationTarget]) -> None:
        """导入校准目标数据"""
        await self.database.save_calibration_targets(targets)

class ExportService:
    """导出服务层"""
    
    def __init__(self):
        self.database = DatabaseManager()
        self.formatters = {
            'csv': CSVFormatter(),
            'excel': ExcelFormatter(),
            'json': JSONFormatter()
        }
        
    async def export_results(self, simulation_id: str, 
                           format: str, options: Dict) -> str:
        """导出模拟结果"""
        results = await self.database.load_simulation_results(simulation_id)
        formatter = self.formatters[format]
        
        output_path = await formatter.format_results(results, options)
        return output_path
```

## 事件驱动架构

### 事件总线系统

```python
class EventBus:
    """事件总线"""
    
    def __init__(self):
        self.subscribers = defaultdict(list)
        
    def subscribe(self, event_type: str, handler: Callable):
        """订阅事件"""
        self.subscribers[event_type].append(handler)
        
    def publish(self, event: Event):
        """发布事件"""
        for handler in self.subscribers[event.type]:
            try:
                handler(event)
            except Exception as e:
                logger.error(f"事件处理失败: {e}")

class SimulationProgressEvent(Event):
    """模拟进度事件"""
    
    def __init__(self, simulation_id: str, progress: float, 
                 current_year: int, eta: datetime):
        self.simulation_id = simulation_id
        self.progress = progress
        self.current_year = current_year
        self.eta = eta

class CalibrationCompleteEvent(Event):
    """校准完成事件"""
    
    def __init__(self, calibration_id: str, results: CalibrationResults):
        self.calibration_id = calibration_id
        self.results = results
```

## 并行计算架构

### 多进程模拟

```python
class ParallelSimulationManager:
    """并行模拟管理器"""
    
    def __init__(self, max_processes: int = None):
        self.max_processes = max_processes or cpu_count()
        self.process_pool = None
        
    async def run_parallel_simulation(self, config: SimulationConfig) -> SimulationResults:
        """并行运行模拟"""
        
        # 分割人群为多个子群
        population_chunks = self.split_population(config.population, self.max_processes)
        
        # 创建进程池
        with ProcessPoolExecutor(max_workers=self.max_processes) as executor:
            # 提交并行任务
            futures = [
                executor.submit(self.run_chunk_simulation, chunk, config)
                for chunk in population_chunks
            ]
            
            # 收集结果
            chunk_results = []
            for future in as_completed(futures):
                result = await future
                chunk_results.append(result)
                
        # 合并结果
        final_results = self.merge_simulation_results(chunk_results)
        return final_results
        
    def split_population(self, population: Population, 
                        num_chunks: int) -> List[Population]:
        """将人群分割为多个子群"""
        chunk_size = len(population.individuals) // num_chunks
        chunks = []
        
        for i in range(num_chunks):
            start_idx = i * chunk_size
            end_idx = start_idx + chunk_size if i < num_chunks - 1 else len(population.individuals)
            
            chunk_individuals = population.individuals[start_idx:end_idx]
            chunk_population = Population(
                individuals=chunk_individuals,
                # ... 其他属性复制
            )
            chunks.append(chunk_population)
            
        return chunks
```
