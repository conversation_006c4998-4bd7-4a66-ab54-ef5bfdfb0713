#!/usr/bin/env python3
"""
配置阿里云镜像源脚本

自动配置pip和Poetry使用阿里云镜像源，提高包下载速度。
"""

import os
import platform
import subprocess
from pathlib import Path


def setup_pip_mirror():
    """配置pip使用阿里云镜像源"""
    print("正在配置pip使用阿里云镜像源...")
    
    try:
        # 配置pip全局源
        subprocess.run([
            "pip", "config", "set", "global.index-url", 
            "https://mirrors.aliyun.com/pypi/simple/"
        ], check=True)
        
        subprocess.run([
            "pip", "config", "set", "global.trusted-host", 
            "mirrors.aliyun.com"
        ], check=True)
        
        print("✅ pip阿里云镜像源配置成功")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ pip镜像源配置失败: {e}")


def setup_poetry_mirror():
    """配置Poetry使用阿里云镜像源"""
    print("正在配置Poetry使用阿里云镜像源...")
    
    try:
        # 添加阿里云源
        subprocess.run([
            "poetry", "source", "add", "aliyun", 
            "https://mirrors.aliyun.com/pypi/simple/", "--priority=primary"
        ], check=True)
        
        print("✅ Poetry阿里云镜像源配置成功")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Poetry镜像源配置失败: {e}")
        print("注意：pyproject.toml中已包含阿里云源配置")


def create_pip_conf():
    """创建pip配置文件"""
    print("正在创建pip配置文件...")
    
    # 确定配置文件路径
    if platform.system() == "Windows":
        config_dir = Path.home() / "pip"
        config_file = config_dir / "pip.ini"
    else:
        config_dir = Path.home() / ".pip"
        config_file = config_dir / "pip.conf"
    
    # 创建配置目录
    config_dir.mkdir(exist_ok=True)
    
    # 写入配置
    config_content = """[global]
index-url = https://mirrors.aliyun.com/pypi/simple/
trusted-host = mirrors.aliyun.com

[install]
trusted-host = mirrors.aliyun.com
"""
    
    config_file.write_text(config_content, encoding="utf-8")
    print(f"✅ pip配置文件已创建: {config_file}")


def verify_configuration():
    """验证镜像源配置"""
    print("\n正在验证镜像源配置...")
    
    try:
        # 检查pip配置
        result = subprocess.run(
            ["pip", "config", "list"], 
            capture_output=True, text=True, check=True
        )
        
        if "mirrors.aliyun.com" in result.stdout:
            print("✅ pip阿里云镜像源配置正确")
        else:
            print("⚠️  pip镜像源配置可能有问题")
            
    except subprocess.CalledProcessError:
        print("⚠️  无法验证pip配置")
    
    # 检查Poetry配置
    try:
        result = subprocess.run(
            ["poetry", "source", "show"], 
            capture_output=True, text=True, check=True
        )
        
        if "aliyun" in result.stdout:
            print("✅ Poetry阿里云镜像源配置正确")
        else:
            print("⚠️  Poetry镜像源配置可能有问题")
            
    except subprocess.CalledProcessError:
        print("⚠️  无法验证Poetry配置")


def main():
    """主函数"""
    print("🚀 开始配置阿里云镜像源...")
    print("=" * 50)
    
    # 配置pip
    setup_pip_mirror()
    create_pip_conf()
    
    # 配置Poetry
    setup_poetry_mirror()
    
    # 验证配置
    verify_configuration()
    
    print("\n" + "=" * 50)
    print("🎉 阿里云镜像源配置完成！")
    print("\n提示：")
    print("- pip安装包时将自动使用阿里云镜像源")
    print("- Poetry已在pyproject.toml中配置阿里云源")
    print("- 如需恢复默认源，请运行: pip config unset global.index-url")


if __name__ == "__main__":
    main()
