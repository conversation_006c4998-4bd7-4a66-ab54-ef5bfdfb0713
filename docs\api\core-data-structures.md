# 核心数据结构 API 文档

## 概述

本文档描述了结直肠癌筛查微观模拟模型的核心数据结构，包括个体（Individual）、人群（Population）和模拟状态（SimulationState）类的API接口。

## 枚举类型

### DiseaseState
疾病状态枚举，定义个体可能的健康状态。

```python
from src.core import DiseaseState

# 可用状态
DiseaseState.NORMAL                 # 正常状态
DiseaseState.LOW_RISK_ADENOMA      # 低风险腺瘤
DiseaseState.HIGH_RISK_ADENOMA     # 高风险腺瘤
DiseaseState.SMALL_SERRATED        # 小锯齿状腺瘤
DiseaseState.LARGE_SERRATED        # 大锯齿状腺瘤
DiseaseState.PRECLINICAL_CANCER    # 临床前癌症
DiseaseState.CLINICAL_CANCER       # 临床癌症
DiseaseState.DEATH_CANCER          # 癌症死亡
DiseaseState.DEATH_OTHER           # 其他原因死亡

# 实用方法
state.is_cancer()    # 判断是否为癌症状态
state.is_adenoma()   # 判断是否为腺瘤状态
state.is_death()     # 判断是否为死亡状态
```

### Gender
性别枚举。

```python
from src.core import Gender

Gender.MALE      # 男性
Gender.FEMALE    # 女性
```

### PathwayType
疾病进展通路类型。

```python
from src.core import PathwayType

PathwayType.ADENOMA_CARCINOMA  # 腺瘤-癌症通路
PathwayType.SERRATED_ADENOMA   # 锯齿状腺瘤通路
```

### CancerStage
癌症分期枚举。

```python
from src.core import CancerStage

CancerStage.STAGE_I    # I期
CancerStage.STAGE_II   # II期
CancerStage.STAGE_III  # III期
CancerStage.STAGE_IV   # IV期

# 实用方法
stage.is_early_stage()  # 判断是否为早期（I-II期）
stage.is_late_stage()   # 判断是否为晚期（III-IV期）
```

## Individual 类

表示模拟中的单个个体。

### 构造函数

```python
from src.core import Individual, Gender, DiseaseState, PathwayType

individual = Individual(
    birth_year=1980,                              # 出生年份
    gender=Gender.MALE,                           # 性别
    individual_id="optional-custom-id",           # 可选：自定义ID
    initial_disease_state=DiseaseState.NORMAL,    # 可选：初始疾病状态
    pathway_type=PathwayType.ADENOMA_CARCINOMA    # 可选：疾病通路类型
)
```

### 主要属性

```python
individual.individual_id           # 个体唯一标识符
individual.birth_year             # 出生年份
individual.gender                 # 性别
individual.current_disease_state  # 当前疾病状态
individual.pathway_type           # 疾病通路类型
individual.cancer_stage           # 癌症分期（如果适用）
individual.health_history         # 健康历史记录列表
```

### 主要方法

```python
# 获取当前年龄
age = individual.get_current_age()
age_in_2030 = individual.get_current_age(reference_year=2030)

# 状态转换
success = individual.transition_to_state(
    new_state=DiseaseState.LOW_RISK_ADENOMA,
    cancer_stage=None,  # 仅癌症状态需要
    additional_data={"cause": "screening_detected"}
)

# 检查存活状态
is_alive = individual.is_alive()

# 获取健康历史
all_transitions = individual.get_health_history_by_type("state_transition")
time_in_adenoma = individual.get_time_in_state(DiseaseState.LOW_RISK_ADENOMA)

# 转换为字典
data = individual.to_dict()
```

## Population 类

管理Individual对象集合的容器类。

### 构造函数

```python
from src.core import Population

# 创建空人群
population = Population()

# 使用初始个体列表创建
population = Population(initial_individuals=[individual1, individual2])
```

### 个体管理

```python
# 添加个体
success = population.add_individual(individual)

# 移除个体
success = population.remove_individual(individual_id)

# 获取个体
individual = population.get_individual(individual_id)

# 检查个体是否存在
exists = individual_id in population

# 获取人群大小
size = population.get_size()
# 或者
size = len(population)

# 检查是否为空
is_empty = population.is_empty()

# 清空人群
population.clear()
```

### 筛选功能

```python
# 按性别筛选
males = population.filter_by_gender(Gender.MALE)

# 按疾病状态筛选
cancer_patients = population.filter_by_disease_state(DiseaseState.CLINICAL_CANCER)

# 按年龄范围筛选
middle_aged = population.filter_by_age_range(40, 65)

# 按自定义条件筛选
high_risk = population.filter_by_criteria(
    lambda ind: ind.current_disease_state.is_adenoma() and ind.get_current_age() > 50
)

# 获取特定群体
alive_individuals = population.get_alive_individuals()
cancer_patients = population.get_cancer_patients()
```

### 批量操作

```python
# 批量状态转换
criteria = lambda ind: ind.current_disease_state == DiseaseState.NORMAL
count = population.batch_transition_states(
    criteria=criteria,
    new_state=DiseaseState.LOW_RISK_ADENOMA,
    cancer_stage=None
)
```

### 统计功能

```python
# 获取统计对象
stats = population.statistics

# 各种分布统计
age_dist = stats.get_age_distribution()
gender_dist = stats.get_gender_distribution()
disease_dist = stats.get_disease_state_distribution()
pathway_dist = stats.get_pathway_distribution()
cancer_stage_dist = stats.get_cancer_stage_distribution()

# 汇总统计
survival_rate = stats.get_survival_rate()
mean_age = stats.get_mean_age()

# 完整统计摘要
summary = stats.get_summary()
```

### 迭代支持

```python
# 迭代所有个体
for individual in population:
    print(individual.individual_id)
```

## SimulationState 类

管理模拟状态、时间和事件的核心类。

### 构造函数

```python
from src.core import SimulationState

simulation = SimulationState(
    simulation_id="optional-custom-id",  # 可选：自定义模拟ID
    start_year=2025,                     # 模拟开始年份
    duration_years=50,                   # 模拟持续时间
    time_step=1.0,                       # 时间步长
    random_seed=42                       # 可选：随机种子
)
```

### 模拟控制

```python
# 开始模拟
simulation.start_simulation()

# 暂停模拟
simulation.pause_simulation()

# 恢复模拟
simulation.resume_simulation()

# 停止模拟
simulation.stop_simulation()

# 推进时间
simulation.advance_time()              # 使用默认时间步长
simulation.advance_time(2.5)           # 自定义时间增量
```

### 状态查询

```python
# 时间信息
current_time = simulation.current_time      # 模拟时间（年）
current_year = simulation.current_year      # 当前年份
progress = simulation.get_progress()        # 进度（0-1）
remaining = simulation.get_remaining_time() # 剩余时间

# 运行状态
is_running = simulation.is_running
is_paused = simulation.is_paused
is_completed = simulation.is_completed

# 实际运行时间
elapsed = simulation.get_elapsed_real_time()
```

### 参数和结果管理

```python
# 设置参数
simulation.set_parameter("population_size", 10000)
simulation.set_parameter("screening_rate", 0.7)

# 获取参数
pop_size = simulation.get_parameter("population_size")
default_value = simulation.get_parameter("nonexistent", default=1000)

# 设置结果
simulation.set_result("total_cancers", 150)
simulation.set_result("cost_per_qaly", 25000)

# 获取结果
cancers = simulation.get_result("total_cancers")
```

### 事件调度

```python
# 调度事件
event_id = simulation.schedule_event(
    event_type="screening",
    delay=2.0,                           # 延迟时间（年）
    target_individual_id="individual-1", # 可选：目标个体
    event_data={"type": "colonoscopy"},  # 可选：事件数据
    priority=0                           # 可选：优先级
)

# 处理下一个事件
event = simulation.process_next_event()

# 注册事件处理器
def handle_screening(event, sim_state):
    print(f"处理筛查事件: {event.event_type}")

simulation.register_event_handler("screening", handle_screening)
```

### 状态持久化

```python
# 保存模拟状态
simulation.save_state("simulation_state.json")

# 加载模拟状态
loaded_simulation = SimulationState.load_state("simulation_state.json")
```

## 使用示例

### 基本使用流程

```python
from src.core import Individual, Population, SimulationState, Gender, DiseaseState

# 1. 创建个体
individuals = [
    Individual(1970 + i, Gender.MALE if i % 2 == 0 else Gender.FEMALE, f"person-{i}")
    for i in range(100)
]

# 2. 创建人群
population = Population(individuals)

# 3. 创建模拟状态
simulation = SimulationState(
    start_year=2025,
    duration_years=20,
    random_seed=42
)

# 4. 开始模拟
simulation.start_simulation()

# 5. 调度事件
for individual in population:
    simulation.schedule_event(
        "screening",
        delay=2.0,
        target_individual_id=individual.individual_id
    )

# 6. 处理事件
while not simulation.event_queue.is_empty():
    event = simulation.process_next_event()
    # 处理事件逻辑...

# 7. 获取结果
final_stats = population.statistics.get_summary()
print(f"模拟完成，处理了{simulation.total_events_processed}个事件")
```

## 数据验证

所有核心数据结构都包含内置的数据验证功能：

```python
from src.utils import validate_age, validate_gender, ValidationError

try:
    # 验证年龄
    age = validate_age(45.5)
    
    # 验证性别
    gender = validate_gender("male")  # 返回Gender.MALE
    
    # 创建个体时自动验证
    individual = Individual(1980, Gender.MALE)
    
except ValidationError as e:
    print(f"验证错误: {e}")
```

## 错误处理

核心数据结构使用特定的异常类型：

```python
from src.utils import (
    ValidationError, AgeValidationError, GenderValidationError,
    StateTransitionError
)

try:
    individual.transition_to_state(DiseaseState.CLINICAL_CANCER)
except StateTransitionError as e:
    print(f"无效的状态转换: {e}")
```
