# 统一项目结构

## 完整目录结构

```
colorectal-screening-model/
├── README.md
├── LICENSE
├── pyproject.toml              # Poetry依赖管理
├── build.spec                  # PyInstaller构建配置
├── docker-compose.yml          # Docker开发环境
├── .github/
│   └── workflows/
│       ├── ci.yml              # 持续集成
│       └── build-release.yml   # 构建发布
├── src/
│   ├── __init__.py
│   ├── core/                   # 核心模拟引擎
│   │   ├── __init__.py
│   │   ├── engine.py           # 主模拟引擎
│   │   ├── individual.py       # 个体类
│   │   ├── population.py       # 人群类
│   │   └── simulation.py       # 模拟管理
│   ├── modules/                # 功能模块
│   │   ├── __init__.py
│   │   ├── disease/            # 疾病模型
│   │   │   ├── __init__.py
│   │   │   ├── disease_model.py
│   │   │   ├── adenoma_pathway.py
│   │   │   ├── serrated_pathway.py
│   │   │   └── enums.py        # 疾病状态枚举
│   │   ├── screening/          # 筛查模型
│   │   │   ├── __init__.py
│   │   │   ├── screening_model.py
│   │   │   ├── tools/
│   │   │   │   ├── fit_tool.py
│   │   │   │   ├── colonoscopy_tool.py
│   │   │   │   └── sigmoidoscopy_tool.py
│   │   │   └── strategies.py
│   │   ├── economics/          # 经济学模型
│   │   │   ├── __init__.py
│   │   │   ├── economic_model.py
│   │   │   ├── cost_calculator.py
│   │   │   └── qaly_calculator.py
│   │   └── population/         # 人群管理
│   │       ├── __init__.py
│   │       ├── population_generator.py
│   │       ├── life_table.py
│   │       └── risk_factors.py
│   ├── calibration/            # 机器学习校准
│   │   ├── __init__.py
│   │   ├── calibration_engine.py
│   │   ├── neural_network.py
│   │   ├── parameter_sampler.py
│   │   └── targets.py
│   ├── interfaces/             # 用户界面
│   │   ├── __init__.py
│   │   ├── desktop/            # 桌面应用
│   │   │   ├── __init__.py
│   │   │   ├── main.py         # 应用入口
│   │   │   ├── app/
│   │   │   │   ├── application.py
│   │   │   │   └── window_manager.py
│   │   │   ├── windows/
│   │   │   │   ├── main_window.py
│   │   │   │   ├── config_wizard.py
│   │   │   │   └── results_analyzer.py
│   │   │   ├── widgets/
│   │   │   │   ├── parameter_panel.py
│   │   │   │   ├── chart_widget.py
│   │   │   │   └── progress_monitor.py
│   │   │   └── resources/
│   │   │       ├── icons/
│   │   │       ├── styles/
│   │   │       └── translations/
│   │   └── cli/                # 命令行界面
│   │       ├── __init__.py
│   │       ├── main.py
│   │       └── commands/
│   ├── services/               # 服务层
│   │   ├── __init__.py
│   │   ├── simulation_service.py
│   │   ├── data_service.py
│   │   ├── export_service.py
│   │   └── calibration_service.py
│   ├── database/               # 数据库层
│   │   ├── __init__.py
│   │   ├── models.py           # SQLAlchemy模型
│   │   ├── database.py         # 数据库管理
│   │   ├── migrations/         # 数据库迁移
│   │   └── repositories/       # 数据访问层
│   └── utils/                  # 工具函数
│       ├── __init__.py
│       ├── validators.py
│       ├── formatters.py
│       ├── file_utils.py
│       └── math_utils.py
├── data/                       # 数据文件
│   ├── life_tables/            # 生命表数据
│   │   ├── china_2020.csv
│   │   └── who_global.csv
│   ├── calibration_targets/    # 校准目标数据
│   │   ├── adenoma_prevalence.json
│   │   ├── cancer_incidence.json
│   │   └── mortality_rates.json
│   ├── screening_parameters/   # 筛查工具参数
│   │   ├── fit_parameters.json
│   │   ├── colonoscopy_parameters.json
│   │   └── sigmoidoscopy_parameters.json
│   └── economic_parameters/    # 经济参数
│       ├── screening_costs.json
│       ├── treatment_costs.json
│       └── qaly_weights.json
├── tests/                      # 测试套件
│   ├── __init__.py
│   ├── conftest.py             # pytest配置
│   ├── unit/                   # 单元测试
│   │   ├── test_individual.py
│   │   ├── test_disease_model.py
│   │   ├── test_screening_model.py
│   │   └── test_economic_model.py
│   ├── integration/            # 集成测试
│   │   ├── test_simulation_engine.py
│   │   ├── test_calibration_engine.py
│   │   └── test_database.py
│   ├── performance/            # 性能测试
│   │   ├── test_large_population.py
│   │   └── test_memory_usage.py
│   └── fixtures/               # 测试数据
│       ├── sample_population.json
│       └── test_parameters.json
├── docs/                       # 文档
│   ├── README.md
│   ├── api/                    # API文档
│   ├── user_guide/             # 用户指南
│   ├── developer_guide/        # 开发者指南
│   └── examples/               # 示例代码
├── scripts/                    # 脚本工具
│   ├── setup_dev_env.py        # 开发环境设置
│   ├── run_calibration.py      # 校准脚本
│   ├── generate_test_data.py   # 测试数据生成
│   └── performance_benchmark.py # 性能基准测试
├── installer/                  # 安装程序配置
│   ├── windows/
│   │   ├── installer.nsi       # NSIS配置
│   │   └── app_icon.ico
│   ├── macos/
│   │   ├── Info.plist
│   │   └── app_icon.icns
│   └── linux/
│       ├── colorectal-screening-model.desktop
│       └── app_icon.png
└── examples/                   # 使用示例
    ├── basic_simulation.py
    ├── calibration_example.py
    ├── screening_comparison.py
    └── economic_analysis.py
```
