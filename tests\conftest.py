"""
pytest配置文件

包含测试的共享配置、fixtures和工具函数。
"""

import pytest
import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))


@pytest.fixture(scope="session")
def test_data_dir():
    """返回测试数据目录路径"""
    return Path(__file__).parent / "fixtures"


@pytest.fixture(scope="session")
def temp_dir(tmp_path_factory):
    """创建临时目录用于测试"""
    return tmp_path_factory.mktemp("test_data")


@pytest.fixture
def sample_config():
    """提供示例配置数据"""
    return {
        "population_size": 1000,
        "simulation_years": 50,
        "random_seed": 42,
    }
