"""
Individual类单元测试

测试Individual类的所有功能，包括创建、属性设置、
状态转换和验证逻辑。
"""

import pytest
from datetime import datetime
from unittest.mock import patch

from src.core.individual import Individual, HealthEvent
from src.core.enums import DiseaseState, Gender, PathwayType, CancerStage
from src.utils.validators import ValidationError


class TestHealthEvent:
    """测试HealthEvent类"""
    
    def test_health_event_creation(self):
        """测试健康事件创建"""
        event = HealthEvent(
            timestamp=datetime.now(),
            event_type="test_event",
            from_state=DiseaseState.NORMAL,
            to_state=DiseaseState.LOW_RISK_ADENOMA,
            age_at_event=45.5
        )
        
        assert event.event_type == "test_event"
        assert event.from_state == DiseaseState.NORMAL
        assert event.to_state == DiseaseState.LOW_RISK_ADENOMA
        assert event.age_at_event == 45.5
    
    def test_health_event_invalid_age(self):
        """测试无效年龄的健康事件"""
        with pytest.raises(ValueError, match="年龄不能为负数"):
            HealthEvent(
                timestamp=datetime.now(),
                event_type="test_event",
                from_state=None,
                to_state=DiseaseState.NORMAL,
                age_at_event=-1.0
            )
    
    def test_health_event_invalid_state(self):
        """测试无效状态的健康事件"""
        with pytest.raises(ValueError, match="目标状态必须是DiseaseState枚举"):
            HealthEvent(
                timestamp=datetime.now(),
                event_type="test_event",
                from_state=None,
                to_state="invalid_state",
                age_at_event=45.0
            )


class TestIndividual:
    """测试Individual类"""
    
    def test_individual_creation_basic(self):
        """测试基本个体创建"""
        individual = Individual(
            birth_year=1980,
            gender=Gender.MALE
        )
        
        assert individual.birth_year == 1980
        assert individual.gender == Gender.MALE
        assert individual.current_disease_state == DiseaseState.NORMAL
        assert individual.pathway_type is None
        assert individual.cancer_stage is None
        assert len(individual.health_history) == 1  # 初始状态记录
        assert individual.individual_id is not None
    
    def test_individual_creation_with_custom_id(self):
        """测试使用自定义ID创建个体"""
        custom_id = "test-individual-123"
        individual = Individual(
            birth_year=1990,
            gender=Gender.FEMALE,
            individual_id=custom_id
        )
        
        assert individual.individual_id == custom_id
    
    def test_individual_creation_with_pathway(self):
        """测试使用疾病通路创建个体"""
        individual = Individual(
            birth_year=1985,
            gender=Gender.MALE,
            pathway_type=PathwayType.ADENOMA_CARCINOMA
        )
        
        assert individual.pathway_type == PathwayType.ADENOMA_CARCINOMA
    
    def test_individual_invalid_birth_year(self):
        """测试无效出生年份"""
        with pytest.raises(ValueError, match="出生年份必须在"):
            Individual(
                birth_year=1800,  # 太早
                gender=Gender.MALE
            )
        
        with pytest.raises(ValueError, match="出生年份必须在"):
            Individual(
                birth_year=2030,  # 太晚
                gender=Gender.MALE
            )
    
    def test_individual_invalid_gender(self):
        """测试无效性别"""
        with pytest.raises(ValueError, match="性别必须是Gender枚举"):
            Individual(
                birth_year=1980,
                gender="invalid_gender"
            )
    
    def test_individual_incompatible_pathway_state(self):
        """测试不兼容的通路和状态组合"""
        with pytest.raises(ValueError, match="不兼容"):
            Individual(
                birth_year=1980,
                gender=Gender.MALE,
                initial_disease_state=DiseaseState.SMALL_SERRATED,
                pathway_type=PathwayType.ADENOMA_CARCINOMA
            )
    
    @patch('src.core.individual.datetime')
    def test_get_current_age(self, mock_datetime):
        """测试获取当前年龄"""
        mock_datetime.now.return_value.year = 2025
        
        individual = Individual(
            birth_year=1980,
            gender=Gender.MALE
        )
        
        assert individual.get_current_age() == 45.0
        assert individual.get_current_age(2030) == 50.0
    
    def test_state_transition_valid(self):
        """测试有效状态转换"""
        individual = Individual(
            birth_year=1980,
            gender=Gender.MALE
        )
        
        # 正常 -> 低风险腺瘤
        success = individual.transition_to_state(DiseaseState.LOW_RISK_ADENOMA)
        assert success is True
        assert individual.current_disease_state == DiseaseState.LOW_RISK_ADENOMA
        assert len(individual.health_history) == 2
    
    def test_state_transition_to_cancer_with_stage(self):
        """测试转换到癌症状态需要分期"""
        individual = Individual(
            birth_year=1980,
            gender=Gender.MALE,
            initial_disease_state=DiseaseState.HIGH_RISK_ADENOMA
        )
        
        # 转换到癌症状态
        success = individual.transition_to_state(
            DiseaseState.PRECLINICAL_CANCER,
            cancer_stage=CancerStage.STAGE_I
        )
        
        assert success is True
        assert individual.current_disease_state == DiseaseState.PRECLINICAL_CANCER
        assert individual.cancer_stage == CancerStage.STAGE_I
    
    def test_state_transition_cancer_without_stage(self):
        """测试癌症状态必须指定分期"""
        individual = Individual(
            birth_year=1980,
            gender=Gender.MALE,
            initial_disease_state=DiseaseState.HIGH_RISK_ADENOMA
        )
        
        with pytest.raises(ValueError, match="癌症状态必须指定癌症分期"):
            individual.transition_to_state(DiseaseState.PRECLINICAL_CANCER)
    
    def test_state_transition_non_cancer_with_stage(self):
        """测试非癌症状态不能指定分期"""
        individual = Individual(
            birth_year=1980,
            gender=Gender.MALE
        )
        
        with pytest.raises(ValueError, match="非癌症状态不能指定癌症分期"):
            individual.transition_to_state(
                DiseaseState.LOW_RISK_ADENOMA,
                cancer_stage=CancerStage.STAGE_I
            )
    
    def test_state_transition_invalid(self):
        """测试无效状态转换"""
        individual = Individual(
            birth_year=1980,
            gender=Gender.MALE
        )
        
        # 正常状态不能直接转换到癌症
        success = individual.transition_to_state(DiseaseState.CLINICAL_CANCER)
        assert success is False
        assert individual.current_disease_state == DiseaseState.NORMAL
    
    def test_state_transition_from_death(self):
        """测试死亡状态不能转换"""
        individual = Individual(
            birth_year=1980,
            gender=Gender.MALE,
            initial_disease_state=DiseaseState.DEATH_OTHER
        )
        
        success = individual.transition_to_state(DiseaseState.NORMAL)
        assert success is False
    
    def test_get_health_history_by_type(self):
        """测试按类型获取健康历史"""
        individual = Individual(
            birth_year=1980,
            gender=Gender.MALE
        )
        
        # 添加状态转换
        individual.transition_to_state(DiseaseState.LOW_RISK_ADENOMA)
        individual.transition_to_state(DiseaseState.HIGH_RISK_ADENOMA)
        
        # 获取状态转换事件
        transitions = individual.get_health_history_by_type("state_transition")
        assert len(transitions) == 2
        
        # 获取初始化事件
        initializations = individual.get_health_history_by_type("initialization")
        assert len(initializations) == 1
    
    def test_get_time_in_state(self):
        """测试计算在状态中的时间"""
        individual = Individual(
            birth_year=1980,
            gender=Gender.MALE
        )
        
        # 模拟时间流逝和状态转换
        with patch.object(individual, 'get_current_age', side_effect=[45.0, 46.0, 47.0]):
            individual.transition_to_state(DiseaseState.LOW_RISK_ADENOMA)
            individual.transition_to_state(DiseaseState.NORMAL)
        
        # 计算在低风险腺瘤状态的时间
        time_in_adenoma = individual.get_time_in_state(DiseaseState.LOW_RISK_ADENOMA)
        assert time_in_adenoma == 1.0  # 46 - 45
    
    def test_is_alive(self):
        """测试存活状态检查"""
        individual = Individual(
            birth_year=1980,
            gender=Gender.MALE
        )
        
        assert individual.is_alive() is True
        
        # 转换到死亡状态
        individual.current_disease_state = DiseaseState.DEATH_OTHER
        assert individual.is_alive() is False
    
    def test_to_dict(self):
        """测试转换为字典"""
        individual = Individual(
            birth_year=1980,
            gender=Gender.MALE,
            pathway_type=PathwayType.ADENOMA_CARCINOMA
        )
        
        data = individual.to_dict()
        
        assert data["birth_year"] == 1980
        assert data["gender"] == "male"
        assert data["current_disease_state"] == "normal"
        assert data["pathway_type"] == "adenoma_carcinoma"
        assert data["cancer_stage"] is None
        assert data["is_alive"] is True
        assert "current_age" in data
        assert "health_history_count" in data
    
    def test_repr(self):
        """测试字符串表示"""
        individual = Individual(
            birth_year=1980,
            gender=Gender.MALE
        )
        
        repr_str = repr(individual)
        assert "Individual" in repr_str
        assert "male" in repr_str
        assert "normal" in repr_str


@pytest.fixture
def sample_individual():
    """创建示例个体用于测试"""
    return Individual(
        birth_year=1980,
        gender=Gender.MALE,
        pathway_type=PathwayType.ADENOMA_CARCINOMA
    )


class TestIndividualIntegration:
    """Individual类集成测试"""
    
    def test_complete_disease_progression(self, sample_individual):
        """测试完整的疾病进展过程"""
        individual = sample_individual
        
        # 正常 -> 低风险腺瘤
        assert individual.transition_to_state(DiseaseState.LOW_RISK_ADENOMA)
        
        # 低风险腺瘤 -> 高风险腺瘤
        assert individual.transition_to_state(DiseaseState.HIGH_RISK_ADENOMA)
        
        # 高风险腺瘤 -> 临床前癌症
        assert individual.transition_to_state(
            DiseaseState.PRECLINICAL_CANCER,
            cancer_stage=CancerStage.STAGE_I
        )
        
        # 临床前癌症 -> 临床癌症
        assert individual.transition_to_state(
            DiseaseState.CLINICAL_CANCER,
            cancer_stage=CancerStage.STAGE_II
        )
        
        # 验证最终状态
        assert individual.current_disease_state == DiseaseState.CLINICAL_CANCER
        assert individual.cancer_stage == CancerStage.STAGE_II
        assert len(individual.health_history) == 5  # 初始 + 4次转换
    
    def test_treatment_recovery(self, sample_individual):
        """测试治疗恢复过程"""
        individual = sample_individual
        
        # 进展到高风险腺瘤
        individual.transition_to_state(DiseaseState.LOW_RISK_ADENOMA)
        individual.transition_to_state(DiseaseState.HIGH_RISK_ADENOMA)
        
        # 通过治疗恢复正常
        assert individual.transition_to_state(DiseaseState.NORMAL)
        assert individual.current_disease_state == DiseaseState.NORMAL
        
        # 验证历史记录
        transitions = individual.get_health_history_by_type("state_transition")
        assert len(transitions) == 3
