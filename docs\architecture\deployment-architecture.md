# 部署架构

## 桌面应用打包

### PyInstaller配置

```python
# build.spec
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['src/interfaces/desktop/main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('src/interfaces/desktop/resources', 'resources'),
        ('data/life_tables', 'data/life_tables'),
        ('data/calibration_targets', 'data/calibration_targets'),
    ],
    hiddenimports=[
        'numpy',
        'pandas',
        'scipy',
        'tensorflow',
        'matplotlib',
        'plotly',
        'PyQt6',
        'sqlalchemy',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='ColorectalScreeningModel',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='src/interfaces/desktop/resources/icons/app_icon.ico'
)
```

### CI/CD流水线

```yaml
# .github/workflows/build-and-release.yml
name: Build and Release

on:
  push:
    tags:
      - 'v*'
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install poetry
        poetry install
    - name: Run tests
      run: |
        poetry run pytest tests/ -v --cov=src --cov-report=xml
    - name: Upload coverage
      uses: codecov/codecov-action@v3

  build-windows:
    needs: test
    runs-on: windows-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install poetry pyinstaller
        poetry install
    - name: Build executable
      run: |
        poetry run pyinstaller build.spec
    - name: Create installer
      run: |
        # 使用NSIS创建Windows安装程序
        makensis installer/windows/installer.nsi
    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: windows-installer
        path: dist/ColorectalScreeningModel-Setup.exe

  build-macos:
    needs: test
    runs-on: macos-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install poetry pyinstaller
        poetry install
    - name: Build executable
      run: |
        poetry run pyinstaller build.spec
    - name: Create DMG
      run: |
        # 创建macOS DMG安装包
        hdiutil create -volname "Colorectal Screening Model" -srcfolder dist/ColorectalScreeningModel.app -ov -format UDZO dist/ColorectalScreeningModel.dmg
    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: macos-dmg
        path: dist/ColorectalScreeningModel.dmg

  build-linux:
    needs: test
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install poetry pyinstaller
        poetry install
        # 安装Linux GUI依赖
        sudo apt-get update
        sudo apt-get install -y libxcb-xinerama0 libxcb-cursor0
    - name: Build executable
      run: |
        poetry run pyinstaller build.spec
    - name: Create AppImage
      run: |
        # 使用linuxdeploy创建AppImage
        wget https://github.com/linuxdeploy/linuxdeploy/releases/download/continuous/linuxdeploy-x86_64.AppImage
        chmod +x linuxdeploy-x86_64.AppImage
        ./linuxdeploy-x86_64.AppImage --appdir AppDir --executable dist/ColorectalScreeningModel --desktop-file installer/linux/colorectal-screening-model.desktop --icon-file resources/icons/app_icon.png --output appimage
    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: linux-appimage
        path: Colorectal_Screening_Model-x86_64.AppImage
```

## 安装程序配置

### Windows NSIS安装程序

```nsis
; installer/windows/installer.nsi
!define APPNAME "结直肠癌筛查微观模拟模型"
!define COMPANYNAME "深圳市南山区慢性病防治院"
!define DESCRIPTION "用于结直肠癌筛查策略评估的微观模拟模型"
!define VERSIONMAJOR 1
!define VERSIONMINOR 0
!define VERSIONBUILD 0

RequestExecutionLevel admin

InstallDir "$PROGRAMFILES\${COMPANYNAME}\${APPNAME}"

Page directory
Page instfiles

Section "install"
    SetOutPath $INSTDIR
    File /r "dist\ColorectalScreeningModel\*"
    
    WriteUninstaller "$INSTDIR\uninstall.exe"
    
    CreateDirectory "$SMPROGRAMS\${COMPANYNAME}"
    CreateShortCut "$SMPROGRAMS\${COMPANYNAME}\${APPNAME}.lnk" "$INSTDIR\ColorectalScreeningModel.exe"
    CreateShortCut "$DESKTOP\${APPNAME}.lnk" "$INSTDIR\ColorectalScreeningModel.exe"
SectionEnd

Section "uninstall"
    Delete "$SMPROGRAMS\${COMPANYNAME}\${APPNAME}.lnk"
    Delete "$DESKTOP\${APPNAME}.lnk"
    RMDir /r "$INSTDIR"
SectionEnd
```
