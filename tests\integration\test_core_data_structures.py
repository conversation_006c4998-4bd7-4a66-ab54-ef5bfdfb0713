"""
核心数据结构集成测试

测试Individual、Population和SimulationState之间的集成功能。
"""

import pytest
from unittest.mock import patch

from src.core import (
    Individual, Population, SimulationState,
    DiseaseState, Gender, PathwayType, CancerStage
)
from src.utils import validate_age, ValidationError


class TestCoreDataStructuresIntegration:
    """核心数据结构集成测试"""
    
    def test_individual_population_integration(self):
        """测试Individual和Population的集成"""
        # 创建多个个体
        individuals = []
        for i in range(5):
            individual = Individual(
                birth_year=1980 + i,
                gender=Gender.MALE if i % 2 == 0 else Gender.FEMALE,
                individual_id=f"test-{i:03d}"
            )
            individuals.append(individual)
        
        # 创建人群
        population = Population(individuals)
        
        # 验证人群统计
        assert population.get_size() == 5
        stats = population.statistics.get_summary()
        assert stats["total_individuals"] == 5
        assert stats["gender_distribution"]["male"] == 3
        assert stats["gender_distribution"]["female"] == 2
        
        # 测试筛选功能
        males = population.filter_by_gender(Gender.MALE)
        assert len(males) == 3
        
        # 测试批量状态转换
        criteria = lambda ind: ind.gender == Gender.MALE
        count = population.batch_transition_states(
            criteria,
            DiseaseState.LOW_RISK_ADENOMA
        )
        assert count == 3
        
        # 验证转换结果
        adenoma_patients = population.filter_by_disease_state(DiseaseState.LOW_RISK_ADENOMA)
        assert len(adenoma_patients) == 3
    
    def test_simulation_population_integration(self):
        """测试SimulationState和Population的集成"""
        # 创建模拟状态
        simulation = SimulationState(
            start_year=2025,
            duration_years=10,
            random_seed=42
        )
        
        # 创建人群
        individuals = [
            Individual(1980, Gender.MALE, f"individual-{i}")
            for i in range(3)
        ]
        population = Population(individuals)
        
        # 将人群信息存储在模拟参数中
        simulation.set_parameter("population", population)
        simulation.set_parameter("population_size", population.get_size())
        
        # 开始模拟
        simulation.start_simulation()
        
        # 为每个个体调度筛查事件
        for individual in population:
            simulation.schedule_event(
                event_type="screening",
                delay=2.0,
                target_individual_id=individual.individual_id,
                event_data={"screening_type": "colonoscopy"}
            )
        
        # 验证事件调度
        assert simulation.event_queue.size() == 3
        
        # 处理事件
        events_processed = 0
        while not simulation.event_queue.is_empty():
            event = simulation.process_next_event()
            if event:
                events_processed += 1
                # 模拟筛查结果处理
                target_individual = population.get_individual(event.target_individual_id)
                if target_individual:
                    # 先从旧索引中移除
                    population._update_indices(target_individual, add=False)
                    # 假设筛查发现腺瘤
                    target_individual.transition_to_state(DiseaseState.LOW_RISK_ADENOMA)
                    # 添加到新索引
                    population._update_indices(target_individual, add=True)
        
        # 验证结果
        assert events_processed == 3
        assert simulation.current_time == 2.0
        
        # 验证人群状态变化
        adenoma_patients = population.filter_by_disease_state(DiseaseState.LOW_RISK_ADENOMA)
        assert len(adenoma_patients) == 3
    
    def test_complete_disease_progression_simulation(self):
        """测试完整的疾病进展模拟"""
        # 创建模拟环境
        simulation = SimulationState(
            start_year=2025,
            duration_years=20,
            time_step=1.0,
            random_seed=42
        )
        
        # 创建个体
        individual = Individual(
            birth_year=1970,
            gender=Gender.MALE,
            individual_id="patient-001",
            pathway_type=PathwayType.ADENOMA_CARCINOMA
        )
        
        population = Population([individual])
        
        # 开始模拟
        simulation.start_simulation()
        
        # 模拟疾病进展时间线
        progression_events = [
            (2.0, DiseaseState.LOW_RISK_ADENOMA, None),
            (5.0, DiseaseState.HIGH_RISK_ADENOMA, None),
            (8.0, DiseaseState.PRECLINICAL_CANCER, CancerStage.STAGE_I),
            (10.0, DiseaseState.CLINICAL_CANCER, CancerStage.STAGE_II),
        ]
        
        # 调度疾病进展事件
        for delay, new_state, cancer_stage in progression_events:
            event_data = {"new_state": new_state.value}
            if cancer_stage:
                event_data["cancer_stage"] = cancer_stage.value
            
            simulation.schedule_event(
                event_type="disease_progression",
                delay=delay,
                target_individual_id=individual.individual_id,
                event_data=event_data
            )
        
        # 注册事件处理器
        def handle_disease_progression(event, sim_state):
            target_individual = population.get_individual(event.target_individual_id)
            if target_individual:
                new_state = DiseaseState(event.event_data["new_state"])
                cancer_stage = None
                if "cancer_stage" in event.event_data:
                    cancer_stage = CancerStage(event.event_data["cancer_stage"])
                
                success = target_individual.transition_to_state(new_state, cancer_stage)
                sim_state.set_result(f"transition_{event.event_time}", success)
        
        simulation.register_event_handler("disease_progression", handle_disease_progression)
        
        # 处理所有事件
        while not simulation.event_queue.is_empty():
            simulation.process_next_event()
        
        # 验证最终状态
        assert individual.current_disease_state == DiseaseState.CLINICAL_CANCER
        assert individual.cancer_stage == CancerStage.STAGE_II
        assert simulation.current_time == 10.0
        
        # 验证健康历史
        assert len(individual.health_history) == 5  # 初始 + 4次转换
        
        # 验证模拟结果
        assert simulation.get_result("transition_2.0") is True
        assert simulation.get_result("transition_5.0") is True
        assert simulation.get_result("transition_8.0") is True
        assert simulation.get_result("transition_10.0") is True
    
    def test_validation_integration(self):
        """测试验证器与核心数据结构的集成"""
        # 测试Individual创建时的验证
        with pytest.raises(ValueError):
            Individual(
                birth_year=1800,  # 无效年份
                gender=Gender.MALE
            )
        
        # 测试有效的Individual创建
        individual = Individual(
            birth_year=1980,
            gender=Gender.MALE
        )
        
        # 测试年龄验证
        age = validate_age(individual.get_current_age())
        assert age >= 0
        
        # 测试状态转换验证
        assert individual.transition_to_state(DiseaseState.LOW_RISK_ADENOMA) is True
        assert individual.transition_to_state(DiseaseState.CLINICAL_CANCER) is False  # 无效转换
    
    def test_population_statistics_with_disease_progression(self):
        """测试人群统计在疾病进展过程中的变化"""
        # 创建初始人群
        individuals = [
            Individual(1970 + i, Gender.MALE if i % 2 == 0 else Gender.FEMALE, f"ind-{i}")
            for i in range(10)
        ]
        population = Population(individuals)
        
        # 初始统计
        initial_stats = population.statistics.get_disease_state_distribution()
        assert initial_stats["normal"] == 10
        
        # 模拟疾病进展
        # 50%的个体发展为腺瘤
        adenoma_criteria = lambda ind: int(ind.individual_id.split('-')[1]) < 5
        population.batch_transition_states(adenoma_criteria, DiseaseState.LOW_RISK_ADENOMA)
        
        # 20%的个体进一步发展为高风险腺瘤
        high_risk_criteria = lambda ind: (
            ind.current_disease_state == DiseaseState.LOW_RISK_ADENOMA and
            int(ind.individual_id.split('-')[1]) < 2
        )
        population.batch_transition_states(high_risk_criteria, DiseaseState.HIGH_RISK_ADENOMA)
        
        # 10%的个体发展为癌症
        cancer_criteria = lambda ind: (
            ind.current_disease_state == DiseaseState.HIGH_RISK_ADENOMA
        )
        population.batch_transition_states(
            cancer_criteria, 
            DiseaseState.PRECLINICAL_CANCER,
            CancerStage.STAGE_I
        )
        
        # 验证最终统计
        final_stats = population.statistics.get_disease_state_distribution()
        assert final_stats["normal"] == 5  # 50%保持正常
        assert final_stats["low_risk_adenoma"] == 3  # 30%为低风险腺瘤
        assert final_stats["preclinical_cancer"] == 2  # 20%为癌症
        
        # 验证癌症分期分布
        cancer_stages = population.statistics.get_cancer_stage_distribution()
        assert cancer_stages["stage_i"] == 2
        
        # 验证存活率
        assert population.statistics.get_survival_rate() == 1.0  # 所有个体都存活
    
    @patch('src.core.individual.datetime')
    def test_age_progression_simulation(self, mock_datetime):
        """测试年龄进展模拟"""
        # 设置初始时间
        mock_datetime.now.return_value.year = 2025
        
        # 创建个体
        individual = Individual(
            birth_year=1980,
            gender=Gender.MALE
        )
        
        # 验证初始年龄
        assert individual.get_current_age() == 45.0
        
        # 模拟时间推进
        mock_datetime.now.return_value.year = 2030
        assert individual.get_current_age() == 50.0
        
        # 使用参考年份
        assert individual.get_current_age(2035) == 55.0
    
    def test_simulation_state_persistence(self):
        """测试模拟状态持久化"""
        import tempfile
        from pathlib import Path
        
        # 创建模拟状态
        simulation = SimulationState(
            start_year=2025,
            duration_years=50,
            random_seed=42
        )
        
        # 设置参数和结果
        simulation.set_parameter("population_size", 1000)
        simulation.set_result("total_cancers", 50)
        
        # 开始模拟并调度事件
        simulation.start_simulation()
        simulation.schedule_event("screening", 5.0, "individual-1")
        simulation.advance_time(2.0)
        
        # 保存状态
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_path = f.name
        
        try:
            simulation.save_state(temp_path)
            
            # 加载状态
            loaded_simulation = SimulationState.load_state(temp_path)
            
            # 验证加载的状态
            assert loaded_simulation.get_parameter("population_size") == 1000
            assert loaded_simulation.get_result("total_cancers") == 50
            assert loaded_simulation.current_time == 2.0
            assert loaded_simulation.event_queue.size() == 1
            
            # 验证可以继续模拟
            loaded_simulation.advance_time(1.0)
            assert loaded_simulation.current_time == 3.0
            
        finally:
            Path(temp_path).unlink()


@pytest.fixture
def sample_simulation_environment():
    """创建示例模拟环境"""
    simulation = SimulationState(
        start_year=2025,
        duration_years=10,
        random_seed=42
    )
    
    individuals = [
        Individual(1970 + i, Gender.MALE if i % 2 == 0 else Gender.FEMALE, f"test-{i}")
        for i in range(5)
    ]
    
    population = Population(individuals)
    
    return {
        "simulation": simulation,
        "population": population,
        "individuals": individuals
    }


class TestComplexScenarios:
    """复杂场景测试"""
    
    def test_screening_intervention_scenario(self, sample_simulation_environment):
        """测试筛查干预场景"""
        env = sample_simulation_environment
        simulation = env["simulation"]
        population = env["population"]
        
        simulation.start_simulation()
        
        # 模拟筛查干预
        # 在第2年进行筛查
        for individual in population:
            simulation.schedule_event(
                "screening",
                2.0,
                individual.individual_id,
                {"screening_type": "colonoscopy", "sensitivity": 0.9}
            )
        
        # 在第5年进行随访筛查
        for individual in population:
            simulation.schedule_event(
                "screening",
                5.0,
                individual.individual_id,
                {"screening_type": "fit", "sensitivity": 0.7}
            )
        
        # 处理筛查事件
        screening_results = []
        
        def handle_screening(event, sim_state):
            individual = population.get_individual(event.target_individual_id)
            if individual:
                # 模拟筛查结果
                screening_results.append({
                    "individual_id": individual.individual_id,
                    "time": event.event_time,
                    "type": event.event_data["screening_type"],
                    "state": individual.current_disease_state.value
                })
        
        simulation.register_event_handler("screening", handle_screening)
        
        # 运行模拟
        while not simulation.event_queue.is_empty():
            simulation.process_next_event()
        
        # 验证筛查结果
        assert len(screening_results) == 10  # 5个个体 × 2次筛查
        assert simulation.current_time == 5.0
        
        # 验证筛查时间点
        screening_times = [result["time"] for result in screening_results]
        assert screening_times.count(2.0) == 5
        assert screening_times.count(5.0) == 5
