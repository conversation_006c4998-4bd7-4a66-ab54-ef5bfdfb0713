version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: colorectal-model-dev
    volumes:
      - .:/app
      - poetry-cache:/tmp/poetry_cache
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
    environment:
      - DISPLAY=${DISPLAY}
      - PYTHONPATH=/app/src
    ports:
      - "8888:8888"  # Ju<PERSON>ter Lab
      - "8080:8080"  # 应用端口
    stdin_open: true
    tty: true
    working_dir: /app
    command: bash

  jupyter:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: colorectal-model-jupyter
    volumes:
      - .:/app
      - poetry-cache:/tmp/poetry_cache
    environment:
      - PYTHONPATH=/app/src
    ports:
      - "8888:8888"
    working_dir: /app
    command: poetry run jupyter lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root --NotebookApp.token=''

  db:
    image: postgres:15-alpine
    container_name: colorectal-model-db
    environment:
      POSTGRES_DB: colorectal_model
      POSTGRES_USER: developer
      POSTGRES_PASSWORD: devpassword
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql

  redis:
    image: redis:7-alpine
    container_name: colorectal-model-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  poetry-cache:
  postgres_data:
  redis_data:
