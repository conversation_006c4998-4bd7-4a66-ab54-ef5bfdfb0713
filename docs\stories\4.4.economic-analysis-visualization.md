# Story 4.4: 经济分析可视化

## Status
Draft

## Story
**As a** 决策者，
**I want** 通过直观的图表查看经济分析结果，
**so that** 快速理解不同策略的经济影响。

## Acceptance Criteria
1. 创建成本效益散点图和成本效益平面
2. 实现预算影响分析图表
3. 生成敏感性分析的龙卷风图
4. 创建成本分解和效益分解图表
5. 实现交互式经济分析面板
6. 添加经济分析结果的导出功能

## Tasks / Subtasks

- [ ] 任务1：实现成本效益散点图和平面 (AC: 1)
  - [ ] 创建src/interfaces/desktop/widgets/cea_plots.py文件
  - [ ] 实现CEAPlotWidget类，生成成本效益图表
  - [ ] 添加成本效益散点图绘制功能
  - [ ] 实现成本效益平面（四象限图）
  - [ ] 创建ICER线和支付意愿阈值线显示
  - [ ] 添加置信椭圆和不确定性区域显示

- [ ] 任务2：实现预算影响分析图表 (AC: 2)
  - [ ] 创建src/interfaces/desktop/widgets/budget_impact_plots.py文件
  - [ ] 实现BudgetImpactWidget类，生成预算影响图表
  - [ ] 添加年度预算影响柱状图
  - [ ] 实现累积预算影响趋势图
  - [ ] 创建预算影响分解图（按成本类别）
  - [ ] 添加预算影响敏感性分析图表

- [ ] 任务3：生成敏感性分析龙卷风图 (AC: 3)
  - [ ] 创建src/interfaces/desktop/widgets/sensitivity_plots.py文件
  - [ ] 实现SensitivityPlotWidget类，生成敏感性图表
  - [ ] 添加单因素敏感性龙卷风图
  - [ ] 实现参数影响排序和可视化
  - [ ] 创建敏感性分析蜘蛛图
  - [ ] 添加概率敏感性分析散点图

- [ ] 任务4：创建成本和效益分解图表 (AC: 4)
  - [ ] 创建src/interfaces/desktop/widgets/decomposition_plots.py文件
  - [ ] 实现DecompositionWidget类，生成分解图表
  - [ ] 添加成本分解饼图和堆叠柱状图
  - [ ] 实现效益分解图表（按年龄组、疾病状态）
  - [ ] 创建成本效益瀑布图
  - [ ] 添加时间序列成本效益分解

- [ ] 任务5：实现交互式经济分析面板 (AC: 5)
  - [ ] 创建src/interfaces/desktop/windows/economic_analysis_panel.py
  - [ ] 实现EconomicAnalysisPanel类，集成所有图表
  - [ ] 添加图表类型选择和切换功能
  - [ ] 实现参数调整和实时更新
  - [ ] 创建图表缩放、平移和标注功能
  - [ ] 添加图表数据表格显示

- [ ] 任务6：添加经济分析结果导出功能 (AC: 6)
  - [ ] 创建src/services/economic_export_service.py文件
  - [ ] 实现EconomicExportService类，处理结果导出
  - [ ] 添加图表导出功能（PNG、SVG、PDF）
  - [ ] 实现数据表格导出（CSV、Excel）
  - [ ] 创建完整报告导出（PDF、Word）
  - [ ] 添加导出格式配置和质量设置

## Dev Notes

### 经济分析可视化数据结构
```python
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
import matplotlib.pyplot as plt
import seaborn as sns
from PyQt6.QtWidgets import QWidget
import plotly.graph_objects as go
import plotly.express as px

@dataclass
class PlotConfig:
    title: str
    xlabel: str
    ylabel: str
    width: int = 800
    height: int = 600
    dpi: int = 300
    style: str = "seaborn"
    color_palette: str = "Set2"

@dataclass
class CEAPlotData:
    strategy_names: List[str]
    costs: List[float]
    qalys: List[float]
    incremental_costs: List[float]
    incremental_qalys: List[float]
    confidence_ellipses: Optional[List[Dict]] = None
```

### 成本效益散点图实现
```python
class CEAPlotWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.figure = plt.figure(figsize=(10, 8))
        self.canvas = FigureCanvas(self.figure)
        self.setup_ui()
    
    def create_cea_scatterplot(
        self, 
        cea_data: CEAPlotData, 
        wtp_threshold: float = 150000
    ):
        """创建成本效益散点图"""
        
        self.figure.clear()
        ax = self.figure.add_subplot(111)
        
        # 绘制散点
        scatter = ax.scatter(
            cea_data.incremental_qalys, 
            cea_data.incremental_costs,
            c=range(len(cea_data.strategy_names)),
            s=100,
            alpha=0.7,
            cmap='Set2'
        )
        
        # 添加策略标签
        for i, strategy in enumerate(cea_data.strategy_names):
            ax.annotate(
                strategy,
                (cea_data.incremental_qalys[i], cea_data.incremental_costs[i]),
                xytext=(5, 5),
                textcoords='offset points',
                fontsize=9
            )
        
        # 添加支付意愿阈值线
        xlim = ax.get_xlim()
        x_threshold = np.linspace(xlim[0], xlim[1], 100)
        y_threshold = x_threshold * wtp_threshold
        ax.plot(x_threshold, y_threshold, 'r--', 
                label=f'WTP阈值: {wtp_threshold:,.0f}元/QALY', linewidth=2)
        
        # 添加象限线
        ax.axhline(y=0, color='k', linestyle='-', alpha=0.3)
        ax.axvline(x=0, color='k', linestyle='-', alpha=0.3)
        
        # 设置标签和标题
        ax.set_xlabel('增量QALY')
        ax.set_ylabel('增量成本 (元)')
        ax.set_title('成本效益平面')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 添加象限标注
        self._add_quadrant_labels(ax)
        
        self.canvas.draw()
    
    def _add_quadrant_labels(self, ax):
        """添加象限标注"""
        xlim = ax.get_xlim()
        ylim = ax.get_ylim()
        
        # 右上象限：成本增加，效果增加
        ax.text(xlim[1]*0.8, ylim[1]*0.8, '成本增加\n效果增加', 
                ha='center', va='center', alpha=0.5, fontsize=10)
        
        # 左上象限：成本增加，效果减少
        ax.text(xlim[0]*0.8, ylim[1]*0.8, '成本增加\n效果减少', 
                ha='center', va='center', alpha=0.5, fontsize=10)
        
        # 左下象限：成本减少，效果减少
        ax.text(xlim[0]*0.8, ylim[0]*0.8, '成本减少\n效果减少', 
                ha='center', va='center', alpha=0.5, fontsize=10)
        
        # 右下象限：成本减少，效果增加（占优）
        ax.text(xlim[1]*0.8, ylim[0]*0.8, '占优策略\n(成本↓效果↑)', 
                ha='center', va='center', alpha=0.5, fontsize=10, 
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.5))
```

### 龙卷风图实现
```python
class SensitivityPlotWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def create_tornado_plot(
        self, 
        sensitivity_data: Dict[str, Dict]
    ):
        """创建敏感性分析龙卷风图"""
        
        parameters = list(sensitivity_data.keys())
        low_values = [sensitivity_data[param]['low_value'] for param in parameters]
        high_values = [sensitivity_data[param]['high_value'] for param in parameters]
        base_value = sensitivity_data[parameters[0]]['base_value']  # 假设基准值相同
        
        # 计算变化幅度
        low_changes = [val - base_value for val in low_values]
        high_changes = [val - base_value for val in high_values]
        
        # 按影响大小排序
        impact_ranges = [abs(high - low) for high, low in zip(high_changes, low_changes)]
        sorted_indices = sorted(range(len(parameters)), 
                              key=lambda i: impact_ranges[i], reverse=True)
        
        # 重新排序数据
        sorted_params = [parameters[i] for i in sorted_indices]
        sorted_low = [low_changes[i] for i in sorted_indices]
        sorted_high = [high_changes[i] for i in sorted_indices]
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(10, 8))
        
        y_pos = np.arange(len(sorted_params))
        
        # 绘制水平条形图
        bars_low = ax.barh(y_pos, sorted_low, height=0.6, 
                          color='lightcoral', alpha=0.7, label='参数下限')
        bars_high = ax.barh(y_pos, sorted_high, height=0.6, 
                           color='lightblue', alpha=0.7, label='参数上限')
        
        # 添加基准线
        ax.axvline(x=0, color='black', linestyle='-', linewidth=2)
        
        # 设置标签
        ax.set_yticks(y_pos)
        ax.set_yticklabels(sorted_params)
        ax.set_xlabel('ICER变化 (元/QALY)')
        ax.set_title('敏感性分析龙卷风图')
        ax.legend()
        ax.grid(True, alpha=0.3, axis='x')
        
        plt.tight_layout()
        return fig
```

### 预算影响分析图表
```python
class BudgetImpactWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def create_budget_impact_chart(
        self, 
        budget_data: Dict[str, List[float]], 
        years: List[int]
    ):
        """创建预算影响分析图表"""
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        # 年度预算影响柱状图
        strategies = list(budget_data.keys())
        x = np.arange(len(years))
        width = 0.8 / len(strategies)
        
        for i, strategy in enumerate(strategies):
            offset = (i - len(strategies)/2 + 0.5) * width
            ax1.bar(x + offset, budget_data[strategy], width, 
                   label=strategy, alpha=0.8)
        
        ax1.set_xlabel('年份')
        ax1.set_ylabel('预算影响 (万元)')
        ax1.set_title('年度预算影响分析')
        ax1.set_xticks(x)
        ax1.set_xticklabels(years)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 累积预算影响趋势图
        for strategy in strategies:
            cumulative_impact = np.cumsum(budget_data[strategy])
            ax2.plot(years, cumulative_impact, marker='o', 
                    linewidth=2, label=strategy)
        
        ax2.set_xlabel('年份')
        ax2.set_ylabel('累积预算影响 (万元)')
        ax2.set_title('累积预算影响趋势')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        return fig
```

### 交互式分析面板
```python
class EconomicAnalysisPanel(QMainWindow):
    def __init__(self):
        super().__init__()
        self.cea_data = None
        self.current_plot_type = "scatterplot"
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 控制面板
        control_panel = self.create_control_panel()
        layout.addWidget(control_panel)
        
        # 图表显示区域
        self.plot_widget = QWidget()
        self.plot_layout = QVBoxLayout(self.plot_widget)
        layout.addWidget(self.plot_widget)
        
        # 数据表格
        self.data_table = QTableWidget()
        layout.addWidget(self.data_table)
    
    def create_control_panel(self) -> QWidget:
        """创建控制面板"""
        panel = QWidget()
        layout = QHBoxLayout(panel)
        
        # 图表类型选择
        self.plot_type_combo = QComboBox()
        self.plot_type_combo.addItems([
            "成本效益散点图", "预算影响分析", "敏感性龙卷风图", 
            "成本分解图", "CEAC曲线"
        ])
        layout.addWidget(QLabel("图表类型:"))
        layout.addWidget(self.plot_type_combo)
        
        # 支付意愿阈值调整
        self.wtp_slider = QSlider(Qt.Orientation.Horizontal)
        self.wtp_slider.setRange(50000, 300000)
        self.wtp_slider.setValue(150000)
        self.wtp_slider.setTickInterval(50000)
        self.wtp_label = QLabel("150,000元/QALY")
        layout.addWidget(QLabel("WTP阈值:"))
        layout.addWidget(self.wtp_slider)
        layout.addWidget(self.wtp_label)
        
        # 导出按钮
        self.export_button = QPushButton("导出图表")
        layout.addWidget(self.export_button)
        
        return panel
    
    def update_plot(self):
        """更新图表显示"""
        if not self.cea_data:
            return
        
        # 清除现有图表
        for i in reversed(range(self.plot_layout.count())):
            self.plot_layout.itemAt(i).widget().setParent(None)
        
        # 根据选择的类型创建新图表
        plot_type = self.plot_type_combo.currentText()
        wtp_threshold = self.wtp_slider.value()
        
        if plot_type == "成本效益散点图":
            plot_widget = CEAPlotWidget()
            plot_widget.create_cea_scatterplot(self.cea_data, wtp_threshold)
        elif plot_type == "预算影响分析":
            plot_widget = BudgetImpactWidget()
            # 需要预算数据
        # ... 其他图表类型
        
        self.plot_layout.addWidget(plot_widget)
```

### Testing
#### 测试文件位置
- `tests/unit/test_cea_plots.py`
- `tests/unit/test_sensitivity_plots.py`
- `tests/unit/test_budget_impact_plots.py`
- `tests/integration/test_economic_visualization.py`

#### 测试标准
- 图表生成功能测试
- 数据可视化准确性测试
- 交互功能测试
- 导出功能测试
- 界面响应性测试

#### 测试框架和模式
- 使用pytest-qt测试GUI组件
- 图像比较测试验证图表输出
- Mock数据测试图表生成
- 性能测试验证大数据集处理

#### 特定测试要求
- 图表生成时间: < 3秒
- 交互响应时间: < 500ms
- 导出功能完整性: 100%成功率
- 图表数据准确性: 与源数据完全一致

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-31 | 1.0 | 初始故事创建 | Scrum Master |

## Dev Agent Record
*此部分将由开发代理在实施过程中填写*

### Agent Model Used
*待填写*

### Debug Log References
*待填写*

### Completion Notes List
*待填写*

### File List
*待填写*

## QA Results
*此部分将由QA代理在完成故事实施的QA审查后填写*
