# 数据库架构

## SQLite数据库设计

### 核心表结构

```sql
-- 个体表
CREATE TABLE individuals (
    id TEXT PRIMARY KEY,
    population_id TEXT NOT NULL,
    age INTEGER NOT NULL,
    gender TEXT NOT NULL,
    birth_year INTEGER NOT NULL,
    death_year INTEGER,
    pathway_type TEXT NOT NULL,
    anatomical_location TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (population_id) REFERENCES populations(id)
);

-- 健康状态历史表
CREATE TABLE health_states (
    id TEXT PRIMARY KEY,
    individual_id TEXT NOT NULL,
    state_type TEXT NOT NULL,  -- normal, low_risk_adenoma, high_risk_adenoma, small_serrated, large_serrated, preclinical_cancer, clinical_cancer
    cancer_stage TEXT,         -- stage_1, stage_2, stage_3, stage_4 (仅当state_type为clinical_cancer时)
    start_time INTEGER NOT NULL,  -- 模拟时间（月）
    end_time INTEGER,
    transition_probability REAL,
    pathway_type TEXT NOT NULL,   -- adenoma_carcinoma, serrated_adenoma
    anatomical_location TEXT,    -- proximal_colon, distal_colon, rectum
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (individual_id) REFERENCES individuals(id),
    CHECK (state_type IN ('normal', 'low_risk_adenoma', 'high_risk_adenoma', 'small_serrated', 'large_serrated', 'preclinical_cancer', 'clinical_cancer')),
    CHECK (cancer_stage IS NULL OR cancer_stage IN ('stage_1', 'stage_2', 'stage_3', 'stage_4')),
    CHECK (pathway_type IN ('adenoma_carcinoma', 'serrated_adenoma'))
);

-- 筛查事件表
CREATE TABLE screening_events (
    id TEXT PRIMARY KEY,
    individual_id TEXT NOT NULL,
    tool_type TEXT NOT NULL,
    event_time INTEGER NOT NULL,  -- 模拟时间（月）
    result TEXT NOT NULL,  -- positive/negative/inadequate
    true_state TEXT NOT NULL,
    follow_up_completed BOOLEAN DEFAULT FALSE,
    costs REAL NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (individual_id) REFERENCES individuals(id)
);

-- 模拟运行表
CREATE TABLE simulation_runs (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    status TEXT NOT NULL,  -- pending/running/completed/failed
    population_id TEXT NOT NULL,
    strategy_id TEXT NOT NULL,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    progress_percent REAL DEFAULT 0,
    current_year INTEGER DEFAULT 0,
    memory_usage_gb REAL,
    cpu_usage_percent REAL,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (population_id) REFERENCES populations(id),
    FOREIGN KEY (strategy_id) REFERENCES screening_strategies(id)
);

-- 经济结果表
CREATE TABLE economic_results (
    id TEXT PRIMARY KEY,
    simulation_id TEXT NOT NULL,
    year INTEGER NOT NULL,
    screening_costs REAL NOT NULL,
    treatment_costs REAL NOT NULL,
    indirect_costs REAL NOT NULL,
    qalys_gained REAL NOT NULL,
    life_years_gained REAL NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (simulation_id) REFERENCES simulation_runs(id)
);
```

### 索引优化

```sql
-- 性能关键索引
CREATE INDEX idx_individuals_population ON individuals(population_id);
CREATE INDEX idx_health_states_individual ON health_states(individual_id);
CREATE INDEX idx_health_states_time ON health_states(start_time, end_time);
CREATE INDEX idx_screening_events_individual ON screening_events(individual_id);
CREATE INDEX idx_screening_events_time ON screening_events(event_time);
CREATE INDEX idx_economic_results_simulation ON economic_results(simulation_id);
CREATE INDEX idx_economic_results_year ON economic_results(year);

-- 复合索引用于复杂查询
CREATE INDEX idx_individuals_age_gender ON individuals(age, gender);
CREATE INDEX idx_screening_events_tool_result ON screening_events(tool_type, result);
```

### 数据分区策略

```python
# 大规模数据分区管理
class DatabasePartitionManager:
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.partition_size = 100000  # 每个分区10万个体
    
    def create_partition_tables(self, simulation_id: str, population_size: int):
        """为大规模模拟创建分区表"""
        num_partitions = math.ceil(population_size / self.partition_size)
        
        for i in range(num_partitions):
            partition_name = f"individuals_{simulation_id}_{i}"
            self.create_individual_partition(partition_name)
    
    def create_individual_partition(self, table_name: str):
        """创建个体数据分区表"""
        sql = f"""
        CREATE TABLE {table_name} (
            LIKE individuals INCLUDING ALL
        );
        """
        # 执行SQL创建分区表
```
