# 前端架构

## PyQt6桌面应用架构

### 应用程序结构

```
src/interfaces/desktop/
├── main.py                 # 应用程序入口点
├── app/
│   ├── __init__.py
│   ├── application.py      # 主应用程序类
│   ├── window_manager.py   # 窗口管理器
│   └── event_bus.py        # 事件总线
├── windows/
│   ├── __init__.py
│   ├── main_window.py      # 主窗口
│   ├── config_wizard.py    # 配置向导
│   ├── simulation_monitor.py # 模拟监控窗口
│   ├── results_analyzer.py # 结果分析窗口
│   └── data_manager.py     # 数据管理窗口
├── widgets/
│   ├── __init__.py
│   ├── parameter_panel.py  # 参数配置面板
│   ├── chart_widget.py     # 图表组件
│   ├── data_table.py       # 数据表格
│   ├── progress_monitor.py # 进度监控
│   └── file_uploader.py    # 文件上传组件
├── models/
│   ├── __init__.py
│   ├── ui_models.py        # UI数据模型
│   └── view_models.py      # 视图模型
├── services/
│   ├── __init__.py
│   ├── simulation_service.py # 模拟服务
│   ├── data_service.py     # 数据服务
│   └── export_service.py   # 导出服务
├── resources/
│   ├── icons/              # 图标资源
│   ├── styles/             # 样式表
│   └── translations/       # 国际化文件
└── utils/
    ├── __init__.py
    ├── validators.py       # 数据验证
    ├── formatters.py       # 数据格式化
    └── helpers.py          # 辅助函数
```

### 主应用程序类

```python
class ColorectalScreeningApp(QApplication):
    """主应用程序类"""

    def __init__(self, sys_argv):
        super().__init__(sys_argv)
        self.setApplicationName("结直肠癌筛查微观模拟模型")
        self.setApplicationVersion("1.0.0")
        self.setOrganizationName("深圳市南山区慢性病防治院")

        # 初始化核心组件
        self.window_manager = WindowManager()
        self.event_bus = EventBus()
        self.simulation_service = SimulationService()
        self.data_service = DataService()

        # 设置应用程序样式
        self.setup_styles()

        # 创建主窗口
        self.main_window = MainWindow()
        self.main_window.show()

    def setup_styles(self):
        """设置应用程序样式"""
        style_path = Path(__file__).parent / "resources" / "styles" / "main.qss"
        with open(style_path, 'r', encoding='utf-8') as f:
            self.setStyleSheet(f.read())
```

### 主窗口设计

```python
class MainWindow(QMainWindow):
    """主窗口类"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("结直肠癌筛查微观模拟模型")
        self.setMinimumSize(1200, 800)

        # 创建中央部件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        # 创建布局
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_tool_bar()
        self.setup_status_bar()

        # 连接信号槽
        self.connect_signals()

    def setup_ui(self):
        """设置用户界面"""
        layout = QHBoxLayout(self.central_widget)

        # 左侧导航面板
        self.nav_panel = NavigationPanel()
        layout.addWidget(self.nav_panel, 0)

        # 中央内容区域
        self.content_stack = QStackedWidget()
        layout.addWidget(self.content_stack, 1)

        # 添加各个页面
        self.dashboard_page = DashboardPage()
        self.config_page = ConfigurationPage()
        self.simulation_page = SimulationPage()
        self.results_page = ResultsPage()
        
        self.content_stack.addWidget(self.dashboard_page)
        self.content_stack.addWidget(self.config_page)
        self.content_stack.addWidget(self.simulation_page)
        self.content_stack.addWidget(self.results_page)
```
