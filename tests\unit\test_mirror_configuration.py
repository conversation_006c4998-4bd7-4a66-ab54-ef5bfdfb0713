"""
测试阿里云镜像源配置

验证项目的镜像源配置是否正确。
"""

import pytest
import toml
from pathlib import Path


class TestMirrorConfiguration:
    """测试镜像源配置的各个方面"""

    def test_poetry_aliyun_source_configured(self):
        """测试Poetry是否配置了阿里云源"""
        project_root = Path(__file__).parent.parent.parent
        pyproject_path = project_root / "pyproject.toml"
        
        assert pyproject_path.exists(), "pyproject.toml文件应该存在"
        
        config = toml.load(pyproject_path)
        
        # 验证是否包含source配置
        assert "tool" in config, "应该包含tool配置"
        assert "poetry" in config["tool"], "应该包含poetry配置"
        
        poetry_config = config["tool"]["poetry"]
        assert "source" in poetry_config, "应该包含source配置"
        
        # 验证阿里云源配置
        sources = poetry_config["source"]
        aliyun_source = None
        
        for source in sources:
            if source.get("name") == "aliyun":
                aliyun_source = source
                break
        
        assert aliyun_source is not None, "应该包含阿里云源配置"
        assert aliyun_source["url"] == "https://mirrors.aliyun.com/pypi/simple/", "阿里云源URL应该正确"
        assert aliyun_source["priority"] == "primary", "阿里云源应该是主要源"

    def test_pip_conf_exists(self):
        """测试pip配置文件是否存在"""
        project_root = Path(__file__).parent.parent.parent
        pip_conf_path = project_root / "pip.conf"
        
        assert pip_conf_path.exists(), "pip.conf文件应该存在"
        
        # 读取配置内容
        config_content = pip_conf_path.read_text(encoding="utf-8")
        
        # 验证配置内容
        assert "mirrors.aliyun.com" in config_content, "应该包含阿里云镜像地址"
        assert "[global]" in config_content, "应该包含global配置段"
        assert "index-url" in config_content, "应该包含index-url配置"
        assert "trusted-host" in config_content, "应该包含trusted-host配置"

    def test_setup_scripts_exist(self):
        """测试镜像源配置脚本是否存在"""
        project_root = Path(__file__).parent.parent.parent
        scripts_dir = project_root / "scripts"
        
        # 检查脚本文件
        expected_scripts = [
            "setup_mirrors.py",
            "setup_mirrors.bat", 
            "setup_mirrors.sh"
        ]
        
        for script in expected_scripts:
            script_path = scripts_dir / script
            assert script_path.exists(), f"{script}脚本应该存在"

    def test_dockerfile_mirror_configuration(self):
        """测试Dockerfile是否配置了阿里云源"""
        project_root = Path(__file__).parent.parent.parent
        dockerfile_path = project_root / "Dockerfile.dev"
        
        assert dockerfile_path.exists(), "Dockerfile.dev应该存在"
        
        dockerfile_content = dockerfile_path.read_text(encoding="utf-8")
        
        # 验证pip镜像源配置
        assert "mirrors.aliyun.com" in dockerfile_content, "Dockerfile应该包含阿里云镜像配置"
        assert "pip config set" in dockerfile_content, "应该包含pip配置命令"

    def test_ci_workflow_mirror_configuration(self):
        """测试CI工作流是否配置了镜像源"""
        project_root = Path(__file__).parent.parent.parent
        ci_workflow_path = project_root / ".github" / "workflows" / "ci.yml"
        
        assert ci_workflow_path.exists(), "CI工作流文件应该存在"
        
        workflow_content = ci_workflow_path.read_text(encoding="utf-8")
        
        # 验证镜像源配置
        assert "Configure mirrors" in workflow_content, "CI工作流应该包含镜像源配置步骤"
        assert "mirrors.aliyun.com" in workflow_content, "应该包含阿里云镜像地址"

    def test_readme_mirror_instructions(self):
        """测试README是否包含镜像源配置说明"""
        project_root = Path(__file__).parent.parent.parent
        readme_path = project_root / "README.md"
        
        assert readme_path.exists(), "README.md应该存在"
        
        readme_content = readme_path.read_text(encoding="utf-8")
        
        # 验证镜像源说明
        assert "阿里云源" in readme_content, "README应该包含阿里云源说明"
        assert "setup_mirrors" in readme_content, "应该包含配置脚本说明"
        assert "mirrors.aliyun.com" in readme_content, "应该包含阿里云镜像地址"
