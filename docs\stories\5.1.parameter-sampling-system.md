# Story 5.1: 参数抽样系统

## Status
Draft

## Story
**As a** 校准引擎，
**I want** 生成大量参数组合用于模型校准，
**so that** 全面探索参数空间并找到最优配置。

## Acceptance Criteria
1. 实现拉丁超立方抽样（LHS）算法
2. 生成10,000个参数组合的抽样空间
3. 实现参数约束和边界条件检查
4. 添加抽样结果的统计分析和可视化
5. 创建参数抽样的可重现性机制
6. 实现抽样效率的性能优化

## Tasks / Subtasks

- [ ] 任务1：实现拉丁超立方抽样算法 (AC: 1)
  - [ ] 创建src/calibration/parameter_sampler.py文件
  - [ ] 实现LatinHypercubeSampler类，核心LHS算法
  - [ ] 添加多维参数空间的均匀抽样功能
  - [ ] 实现抽样点的正交性验证
  - [ ] 创建抽样质量评估指标计算
  - [ ] 添加抽样算法的单元测试

- [ ] 任务2：实现大规模参数组合生成 (AC: 2)
  - [ ] 扩展抽样器支持10,000+参数组合生成
  - [ ] 实现内存高效的批量抽样处理
  - [ ] 添加抽样进度监控和报告功能
  - [ ] 创建抽样结果的存储和索引系统
  - [ ] 实现抽样数据的压缩和序列化
  - [ ] 添加大规模抽样的性能基准测试

- [ ] 任务3：实现参数约束和边界检查 (AC: 3)
  - [ ] 创建src/calibration/parameter_constraints.py文件
  - [ ] 实现ParameterConstraints类，管理参数约束
  - [ ] 添加参数范围边界检查功能
  - [ ] 实现参数间相关性约束验证
  - [ ] 创建约束违反的检测和修正机制
  - [ ] 添加约束条件的配置文件支持

- [ ] 任务4：添加抽样统计分析和可视化 (AC: 4)
  - [ ] 创建src/calibration/sampling_analytics.py文件
  - [ ] 实现SamplingAnalyzer类，分析抽样质量
  - [ ] 添加抽样分布的统计检验功能
  - [ ] 实现抽样覆盖度和均匀性评估
  - [ ] 创建抽样结果的多维可视化
  - [ ] 添加抽样质量报告生成功能

- [ ] 任务5：创建抽样可重现性机制 (AC: 5)
  - [ ] 实现随机种子管理和版本控制
  - [ ] 添加抽样配置的完整记录功能
  - [ ] 创建抽样结果的哈希验证机制
  - [ ] 实现抽样过程的完整重现功能
  - [ ] 添加抽样历史的追踪和比较
  - [ ] 创建抽样实验的元数据管理

- [ ] 任务6：实现抽样性能优化 (AC: 6)
  - [ ] 优化LHS算法的计算效率
  - [ ] 实现多线程并行抽样处理
  - [ ] 添加内存使用优化和垃圾回收
  - [ ] 创建抽样缓存和重用机制
  - [ ] 实现抽样算法的向量化计算
  - [ ] 添加性能监控和瓶颈分析工具

## Dev Notes

### 拉丁超立方抽样数据结构
```python
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Any
import numpy as np
from scipy.stats import qmc
import hashlib

@dataclass
class ParameterDefinition:
    name: str
    min_value: float
    max_value: float
    distribution: str = "uniform"  # uniform, normal, lognormal, etc.
    constraints: Optional[List[str]] = None
    description: str = ""

@dataclass
class SamplingConfig:
    parameters: List[ParameterDefinition]
    n_samples: int
    random_seed: int
    sampling_method: str = "lhs"
    optimization_criterion: str = "maximin"  # maximin, correlation, etc.
    
@dataclass
class SamplingResult:
    samples: np.ndarray
    parameter_names: List[str]
    config: SamplingConfig
    quality_metrics: Dict[str, float]
    generation_time: float
    hash_signature: str
```

### 拉丁超立方抽样实现
```python
class LatinHypercubeSampler:
    def __init__(self, config: SamplingConfig):
        self.config = config
        self.parameters = {p.name: p for p in config.parameters}
        self.n_dimensions = len(config.parameters)
        self.rng = np.random.RandomState(config.random_seed)
    
    def generate_samples(self) -> SamplingResult:
        """生成拉丁超立方抽样"""
        start_time = time.time()
        
        # 使用scipy的拉丁超立方抽样
        sampler = qmc.LatinHypercube(
            d=self.n_dimensions, 
            seed=self.config.random_seed,
            optimization=self.config.optimization_criterion
        )
        
        # 生成[0,1]区间的样本
        unit_samples = sampler.random(n=self.config.n_samples)
        
        # 转换到实际参数范围
        scaled_samples = self._scale_samples(unit_samples)
        
        # 应用约束条件
        constrained_samples = self._apply_constraints(scaled_samples)
        
        # 计算质量指标
        quality_metrics = self._calculate_quality_metrics(constrained_samples)
        
        generation_time = time.time() - start_time
        
        # 生成哈希签名
        hash_signature = self._generate_hash_signature(constrained_samples)
        
        return SamplingResult(
            samples=constrained_samples,
            parameter_names=[p.name for p in self.config.parameters],
            config=self.config,
            quality_metrics=quality_metrics,
            generation_time=generation_time,
            hash_signature=hash_signature
        )
    
    def _scale_samples(self, unit_samples: np.ndarray) -> np.ndarray:
        """将[0,1]区间样本缩放到实际参数范围"""
        scaled_samples = np.zeros_like(unit_samples)
        
        for i, param in enumerate(self.config.parameters):
            if param.distribution == "uniform":
                scaled_samples[:, i] = (
                    param.min_value + 
                    unit_samples[:, i] * (param.max_value - param.min_value)
                )
            elif param.distribution == "normal":
                # 使用正态分布的逆累积分布函数
                from scipy.stats import norm
                scaled_samples[:, i] = norm.ppf(
                    unit_samples[:, i], 
                    loc=(param.min_value + param.max_value) / 2,
                    scale=(param.max_value - param.min_value) / 6  # 3-sigma规则
                )
            elif param.distribution == "lognormal":
                from scipy.stats import lognorm
                scaled_samples[:, i] = lognorm.ppf(
                    unit_samples[:, i],
                    s=0.5,  # 形状参数
                    scale=np.exp((np.log(param.min_value) + np.log(param.max_value)) / 2)
                )
        
        return scaled_samples
    
    def _calculate_quality_metrics(self, samples: np.ndarray) -> Dict[str, float]:
        """计算抽样质量指标"""
        metrics = {}
        
        # 计算最小距离（Maximin准则）
        from scipy.spatial.distance import pdist
        distances = pdist(samples)
        metrics['min_distance'] = np.min(distances)
        metrics['mean_distance'] = np.mean(distances)
        
        # 计算相关性
        correlation_matrix = np.corrcoef(samples.T)
        off_diagonal = correlation_matrix[np.triu_indices_from(correlation_matrix, k=1)]
        metrics['max_correlation'] = np.max(np.abs(off_diagonal))
        metrics['mean_correlation'] = np.mean(np.abs(off_diagonal))
        
        # 计算覆盖度（每个维度的分布均匀性）
        coverage_scores = []
        for i in range(samples.shape[1]):
            # 使用Kolmogorov-Smirnov检验
            from scipy.stats import kstest
            ks_stat, _ = kstest(samples[:, i], 'uniform')
            coverage_scores.append(1 - ks_stat)  # 转换为覆盖度分数
        
        metrics['mean_coverage'] = np.mean(coverage_scores)
        metrics['min_coverage'] = np.min(coverage_scores)
        
        return metrics
```

### 参数约束系统
```python
class ParameterConstraints:
    def __init__(self, constraint_definitions: List[Dict]):
        self.constraints = constraint_definitions
        self.constraint_functions = self._compile_constraints()
    
    def _compile_constraints(self) -> List[callable]:
        """编译约束条件为可执行函数"""
        functions = []
        
        for constraint in self.constraints:
            if constraint['type'] == 'linear':
                # 线性约束: a1*x1 + a2*x2 + ... <= b
                def linear_constraint(samples, coeffs=constraint['coefficients'], 
                                    bound=constraint['bound']):
                    return np.dot(samples, coeffs) <= bound
                functions.append(linear_constraint)
            
            elif constraint['type'] == 'ratio':
                # 比例约束: x1/x2 <= ratio
                def ratio_constraint(samples, idx1=constraint['param1_idx'], 
                                   idx2=constraint['param2_idx'], 
                                   max_ratio=constraint['max_ratio']):
                    return samples[:, idx1] / samples[:, idx2] <= max_ratio
                functions.append(ratio_constraint)
            
            elif constraint['type'] == 'custom':
                # 自定义约束函数
                exec(constraint['function_code'])
                functions.append(locals()[constraint['function_name']])
        
        return functions
    
    def check_constraints(self, samples: np.ndarray) -> np.ndarray:
        """检查样本是否满足所有约束条件"""
        valid_mask = np.ones(samples.shape[0], dtype=bool)
        
        for constraint_func in self.constraint_functions:
            constraint_satisfied = constraint_func(samples)
            valid_mask &= constraint_satisfied
        
        return valid_mask
    
    def repair_samples(self, samples: np.ndarray) -> np.ndarray:
        """修复违反约束的样本"""
        repaired_samples = samples.copy()
        
        for i, constraint_func in enumerate(self.constraint_functions):
            violated_mask = ~constraint_func(repaired_samples)
            
            if np.any(violated_mask):
                # 简单修复策略：重新抽样违反约束的样本
                n_violations = np.sum(violated_mask)
                
                # 在约束范围内重新抽样
                for j in range(repaired_samples.shape[1]):
                    param = self.config.parameters[j]
                    repaired_samples[violated_mask, j] = np.random.uniform(
                        param.min_value, param.max_value, n_violations
                    )
        
        return repaired_samples
```

### 抽样质量分析
```python
class SamplingAnalyzer:
    def __init__(self):
        self.quality_thresholds = {
            'min_distance': 0.01,      # 最小距离阈值
            'max_correlation': 0.1,    # 最大相关性阈值
            'min_coverage': 0.9        # 最小覆盖度阈值
        }
    
    def analyze_sampling_quality(self, sampling_result: SamplingResult) -> Dict:
        """分析抽样质量"""
        analysis = {
            'overall_quality': self._calculate_overall_quality(sampling_result),
            'dimension_analysis': self._analyze_dimensions(sampling_result),
            'correlation_analysis': self._analyze_correlations(sampling_result),
            'space_filling': self._analyze_space_filling(sampling_result),
            'recommendations': self._generate_recommendations(sampling_result)
        }
        
        return analysis
    
    def _calculate_overall_quality(self, result: SamplingResult) -> Dict:
        """计算总体质量评分"""
        metrics = result.quality_metrics
        
        # 归一化各项指标到[0,1]区间
        distance_score = min(metrics['min_distance'] / self.quality_thresholds['min_distance'], 1.0)
        correlation_score = max(0, 1 - metrics['max_correlation'] / self.quality_thresholds['max_correlation'])
        coverage_score = metrics['min_coverage']
        
        # 加权平均
        overall_score = (distance_score * 0.4 + correlation_score * 0.3 + coverage_score * 0.3)
        
        return {
            'overall_score': overall_score,
            'distance_score': distance_score,
            'correlation_score': correlation_score,
            'coverage_score': coverage_score,
            'quality_grade': self._get_quality_grade(overall_score)
        }
    
    def _get_quality_grade(self, score: float) -> str:
        """根据分数给出质量等级"""
        if score >= 0.9:
            return "优秀"
        elif score >= 0.8:
            return "良好"
        elif score >= 0.7:
            return "中等"
        elif score >= 0.6:
            return "及格"
        else:
            return "不及格"
    
    def visualize_sampling_results(self, result: SamplingResult) -> Dict:
        """生成抽样结果可视化"""
        import matplotlib.pyplot as plt
        import seaborn as sns
        
        visualizations = {}
        
        # 1. 参数分布直方图
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        axes = axes.flatten()
        
        for i, param_name in enumerate(result.parameter_names[:6]):  # 最多显示6个参数
            if i < len(axes):
                axes[i].hist(result.samples[:, i], bins=50, alpha=0.7)
                axes[i].set_title(f'{param_name} 分布')
                axes[i].set_xlabel('参数值')
                axes[i].set_ylabel('频数')
        
        plt.tight_layout()
        visualizations['parameter_distributions'] = fig
        
        # 2. 参数相关性热图
        fig, ax = plt.subplots(figsize=(10, 8))
        correlation_matrix = np.corrcoef(result.samples.T)
        sns.heatmap(correlation_matrix, 
                   xticklabels=result.parameter_names,
                   yticklabels=result.parameter_names,
                   annot=True, cmap='coolwarm', center=0, ax=ax)
        ax.set_title('参数相关性矩阵')
        visualizations['correlation_heatmap'] = fig
        
        # 3. 二维散点图矩阵（前4个参数）
        if len(result.parameter_names) >= 2:
            fig, axes = plt.subplots(4, 4, figsize=(12, 12))
            for i in range(min(4, len(result.parameter_names))):
                for j in range(min(4, len(result.parameter_names))):
                    if i == j:
                        axes[i, j].hist(result.samples[:, i], bins=30, alpha=0.7)
                    else:
                        axes[i, j].scatter(result.samples[:, j], result.samples[:, i], 
                                         alpha=0.5, s=1)
                    
                    if i == 3:
                        axes[i, j].set_xlabel(result.parameter_names[j])
                    if j == 0:
                        axes[i, j].set_ylabel(result.parameter_names[i])
            
            plt.tight_layout()
            visualizations['scatter_matrix'] = fig
        
        return visualizations
```

### Testing
#### 测试文件位置
- `tests/unit/test_parameter_sampler.py`
- `tests/unit/test_parameter_constraints.py`
- `tests/unit/test_sampling_analytics.py`
- `tests/integration/test_sampling_system.py`

#### 测试标准
- LHS算法正确性测试
- 大规模抽样性能测试
- 参数约束验证测试
- 抽样质量指标测试
- 可重现性验证测试

#### 测试框架和模式
- 使用已知分布验证抽样质量
- 统计检验验证抽样均匀性
- 性能基准测试验证效率
- 重现性测试验证一致性

#### 特定测试要求
- 抽样均匀性: KS检验p值 > 0.05
- 抽样效率: 10,000样本生成 < 30秒
- 内存使用: 10,000样本 < 1GB内存
- 可重现性: 相同种子100%一致结果

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-31 | 1.0 | 初始故事创建 | Scrum Master |

## Dev Agent Record
*此部分将由开发代理在实施过程中填写*

### Agent Model Used
*待填写*

### Debug Log References
*待填写*

### Completion Notes List
*待填写*

### File List
*待填写*

## QA Results
*此部分将由QA代理在完成故事实施的QA审查后填写*
