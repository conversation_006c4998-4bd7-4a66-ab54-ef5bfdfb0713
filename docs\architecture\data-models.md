# 数据模型

## 疾病进展通路图

```mermaid
graph TD
    subgraph "腺瘤-癌变通路 (85%病例)"
        A1[正常] --> A2[低风险腺瘤]
        A2 --> A3[高风险腺瘤]
        A3 --> A4[临床前癌症]
        A4 --> A5[临床癌症 I期]
        A5 --> A6[临床癌症 II期]
        A6 --> A7[临床癌症 III期]
        A7 --> A8[临床癌症 IV期]
    end

    subgraph "锯齿状腺瘤通路 (15%病例)"
        S1[正常] --> S2[小锯齿状腺瘤]
        S2 --> S3[大锯齿状腺瘤]
        S3 --> S4[临床前癌症]
        S4 --> S5[临床癌症 I期]
        S5 --> S6[临床癌症 II期]
        S6 --> S7[临床癌症 III期]
        S7 --> S8[临床癌症 IV期]
    end

    subgraph "通路分配"
        N[新个体] --> |85%| A1
        N --> |15%| S1
    end

    style A1 fill:#e1f5fe
    style S1 fill:#e1f5fe
    style A8 fill:#ffebee
    style S8 fill:#ffebee
```

## 核心实体关系

```mermaid
erDiagram
    Individual ||--o{ HealthState : has
    Individual ||--o{ RiskFactor : has
    Individual ||--o{ ScreeningEvent : undergoes
    Individual }|--|| Population : belongs_to

    Population ||--o{ SimulationRun : simulated_in
    SimulationRun ||--o{ ScreeningStrategy : uses
    SimulationRun ||--o{ EconomicResult : produces

    ScreeningStrategy ||--o{ ScreeningTool : includes
    ScreeningTool ||--o{ PerformanceParameter : has

    DiseaseModel ||--o{ DiseaseState : defines
    DiseaseModel ||--o{ TransitionProbability : contains

    CalibrationRun ||--o{ ParameterSet : generates
    CalibrationRun ||--o{ CalibrationTarget : aims_for

    HealthState {
        string state_type "normal, low_risk_adenoma, high_risk_adenoma, small_serrated, large_serrated, preclinical_cancer, clinical_cancer"
        string cancer_stage "stage_1, stage_2, stage_3, stage_4"
        string pathway_type "adenoma_carcinoma, serrated_adenoma"
    }
```

## 数据模型详细定义

### Individual（个体）
```python
@dataclass
class Individual:
    id: UUID
    age: int
    gender: Gender
    birth_year: int
    death_year: Optional[int]
    risk_factors: Dict[RiskFactorType, float]
    health_history: List[HealthState]
    screening_history: List[ScreeningEvent]
    current_disease_state: DiseaseState
    pathway_type: PathwayType  # 腺瘤-癌变 vs 锯齿状腺瘤
    anatomical_location: AnatomicalLocation
    cancer_stage: Optional[CancerStage]  # 癌症分期（I-IV期）
    created_at: datetime
    updated_at: datetime

# 疾病状态枚举
class DiseaseState(Enum):
    # 通用状态
    NORMAL = "normal"

    # 腺瘤-癌变通路状态
    LOW_RISK_ADENOMA = "low_risk_adenoma"
    HIGH_RISK_ADENOMA = "high_risk_adenoma"

    # 锯齿状腺瘤通路状态
    SMALL_SERRATED = "small_serrated"
    LARGE_SERRATED = "large_serrated"

    # 共同的癌症状态
    PRECLINICAL_CANCER = "preclinical_cancer"
    CLINICAL_CANCER = "clinical_cancer"

# 癌症分期枚举
class CancerStage(Enum):
    STAGE_I = "stage_1"
    STAGE_II = "stage_2"
    STAGE_III = "stage_3"
    STAGE_IV = "stage_4"

# 疾病通路类型
class PathwayType(Enum):
    ADENOMA_CARCINOMA = "adenoma_carcinoma"  # 85%病例
    SERRATED_ADENOMA = "serrated_adenoma"    # 15%病例

# Individual类的扩展方法
class Individual:
    # ... 其他属性和方法 ...

    def transition_to_state(self, new_state: DiseaseState,
                           transition_time: int,
                           new_stage: Optional[CancerStage] = None) -> None:
        """转换到新的疾病状态"""

        # 记录当前状态的结束时间
        if self.health_history:
            current_health_state = self.health_history[-1]
            current_health_state.end_time = transition_time

        # 创建新的健康状态记录
        new_health_state = HealthState(
            individual_id=self.id,
            state_type=new_state,
            cancer_stage=new_stage,
            start_time=transition_time,
            pathway_type=self.pathway_type,
            anatomical_location=self.anatomical_location
        )

        # 更新个体状态
        self.current_disease_state = new_state
        self.cancer_stage = new_stage
        self.health_history.append(new_health_state)
        self.updated_at = datetime.now()

    def get_state_entry_time(self, state: DiseaseState) -> Optional[int]:
        """获取进入指定状态的时间"""
        for health_state in reversed(self.health_history):
            if health_state.state_type == state:
                return health_state.start_time
        return None

    def get_time_in_current_state(self, current_time: int) -> int:
        """获取在当前状态的停留时间"""
        entry_time = self.get_state_entry_time(self.current_disease_state)
        if entry_time is not None:
            return current_time - entry_time
        return 0

    def is_in_adenoma_pathway(self) -> bool:
        """判断是否在腺瘤-癌变通路"""
        return self.pathway_type == PathwayType.ADENOMA_CARCINOMA

    def is_in_serrated_pathway(self) -> bool:
        """判断是否在锯齿状腺瘤通路"""
        return self.pathway_type == PathwayType.SERRATED_ADENOMA

    def has_adenoma(self) -> bool:
        """判断是否有腺瘤"""
        adenoma_states = {
            DiseaseState.LOW_RISK_ADENOMA,
            DiseaseState.HIGH_RISK_ADENOMA,
            DiseaseState.SMALL_SERRATED,
            DiseaseState.LARGE_SERRATED
        }
        return self.current_disease_state in adenoma_states

    def has_cancer(self) -> bool:
        """判断是否有癌症"""
        cancer_states = {
            DiseaseState.PRECLINICAL_CANCER,
            DiseaseState.CLINICAL_CANCER
        }
        return self.current_disease_state in cancer_states

    def get_cancer_stage_description(self) -> str:
        """获取癌症分期描述"""
        if not self.has_cancer():
            return "无癌症"

        if self.current_disease_state == DiseaseState.PRECLINICAL_CANCER:
            return "临床前癌症"

        if self.cancer_stage:
            stage_descriptions = {
                CancerStage.STAGE_I: "I期癌症",
                CancerStage.STAGE_II: "II期癌症",
                CancerStage.STAGE_III: "III期癌症",
                CancerStage.STAGE_IV: "IV期癌症"
            }
            return stage_descriptions.get(self.cancer_stage, "未知分期癌症")

        return "临床癌症（未分期）"

# 健康状态数据类
@dataclass
class HealthState:
    individual_id: UUID
    state_type: DiseaseState
    cancer_stage: Optional[CancerStage]
    start_time: int  # 模拟时间（月）
    end_time: Optional[int] = None
    pathway_type: PathwayType = None
    anatomical_location: Optional[str] = None
    transition_probability: Optional[float] = None
    created_at: datetime = field(default_factory=datetime.now)
```

### Population（人群）
```python
@dataclass
class Population:
    id: UUID
    name: str
    size: int
    age_distribution: Dict[int, float]
    gender_ratio: float  # 男性比例
    life_table: LifeTable
    risk_factor_distribution: Dict[RiskFactorType, Distribution]
    individuals: List[Individual]
    created_at: datetime
```

### ScreeningStrategy（筛查策略）
```python
@dataclass
class ScreeningStrategy:
    id: UUID
    name: str
    description: str
    start_age: int
    end_age: int
    tools: List[ScreeningTool]
    intervals: Dict[ScreeningTool, int]  # 筛查间隔（月）
    compliance_rates: Dict[ScreeningTool, float]
    follow_up_compliance: float  # 阳性后肠镜依从性
    sequential_implementation: bool
    created_at: datetime
```

### EconomicParameters（经济参数）
```python
@dataclass
class EconomicParameters:
    id: UUID
    screening_costs: Dict[ScreeningTool, float]
    treatment_costs: Dict[CancerStage, float]
    indirect_costs: Dict[str, float]
    discount_rate: float = 0.03  # 3%年度折现率
    qaly_weights: Dict[HealthState, float]
    currency: str = "CNY"
    reference_year: int
    created_at: datetime
```
