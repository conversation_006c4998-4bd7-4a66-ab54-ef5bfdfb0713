# 开发指南

## 开发环境设置

### 环境准备

```bash
# 1. 克隆仓库
git clone https://github.com/your-org/colorectal-screening-model.git
cd colorectal-screening-model

# 2. 安装Python 3.9+
# 推荐使用pyenv管理Python版本
pyenv install 3.9.16
pyenv local 3.9.16

# 3. 安装Poetry
curl -sSL https://install.python-poetry.org | python3 -

# 4. 安装项目依赖
poetry install

# 5. 激活虚拟环境
poetry shell

# 6. 安装pre-commit钩子
pre-commit install
```

### Docker开发环境

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    volumes:
      - .:/app
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
    environment:
      - DISPLAY=${DISPLAY}
    ports:
      - "8000:8000"
    command: python src/interfaces/desktop/main.py

  jupyter:
    build:
      context: .
      dockerfile: Dockerfile.dev
    volumes:
      - .:/app
    ports:
      - "8888:8888"
    command: jupyter lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root
```

## 代码规范

### Python代码风格

```python
# pyproject.toml
[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
```

### 提交规范

```bash
# 提交消息格式
<type>(<scope>): <subject>

<body>

<footer>

# 类型说明
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 重构
test: 测试相关
chore: 构建过程或辅助工具的变动

# 示例
feat(disease): 添加锯齿状腺瘤疾病状态支持

- 实现锯齿状腺瘤通路的疾病状态枚举
- 添加锯齿状腺瘤进展概率计算
- 更新Individual类以支持通路类型区分

Closes #123
```

## 测试策略

### 测试配置

```python
# pytest.ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --cov=src
    --cov-report=term-missing
    --cov-report=html
    --cov-report=xml
markers =
    unit: marks tests as unit tests
    integration: marks tests as integration tests
    performance: marks tests as performance tests
```

### 单元测试示例

```python
# tests/unit/test_individual.py
import pytest
from datetime import datetime
from src.core.individual import Individual
from src.modules.disease.enums import DiseaseState, PathwayType, CancerStage

class TestIndividual:
    
    def test_individual_creation(self):
        """测试个体创建"""
        individual = Individual(
            age=50,
            gender="male",
            birth_year=1973,
            pathway_type=PathwayType.ADENOMA_CARCINOMA
        )
        
        assert individual.age == 50
        assert individual.gender == "male"
        assert individual.current_disease_state == DiseaseState.NORMAL
        assert individual.pathway_type == PathwayType.ADENOMA_CARCINOMA
    
    def test_state_transition(self):
        """测试疾病状态转换"""
        individual = Individual(
            age=50,
            gender="male", 
            birth_year=1973,
            pathway_type=PathwayType.ADENOMA_CARCINOMA
        )
        
        # 转换到低风险腺瘤状态
        individual.transition_to_state(
            DiseaseState.LOW_RISK_ADENOMA, 
            transition_time=600  # 50岁
        )
        
        assert individual.current_disease_state == DiseaseState.LOW_RISK_ADENOMA
        assert len(individual.health_history) == 1
        assert individual.health_history[0].state_type == DiseaseState.LOW_RISK_ADENOMA
    
    def test_cancer_stage_progression(self):
        """测试癌症分期进展"""
        individual = Individual(
            age=60,
            gender="female",
            birth_year=1963,
            pathway_type=PathwayType.SERRATED_ADENOMA
        )
        
        # 转换到临床癌症I期
        individual.transition_to_state(
            DiseaseState.CLINICAL_CANCER,
            transition_time=720,  # 60岁
            new_stage=CancerStage.STAGE_I
        )
        
        assert individual.has_cancer()
        assert individual.cancer_stage == CancerStage.STAGE_I
        assert individual.get_cancer_stage_description() == "I期癌症"

@pytest.fixture
def sample_population():
    """创建样本人群用于测试"""
    individuals = []
    for i in range(1000):
        individual = Individual(
            age=50 + (i % 25),  # 50-74岁
            gender="male" if i % 2 == 0 else "female",
            birth_year=1973 - (i % 25),
            pathway_type=PathwayType.ADENOMA_CARCINOMA if i % 5 != 0 else PathwayType.SERRATED_ADENOMA
        )
        individuals.append(individual)
    
    return Population(individuals=individuals)
```

### 性能测试

```python
# tests/performance/test_large_population.py
import pytest
import time
import psutil
import os
from src.core.engine import MicrosimulationEngine
from src.core.population import Population

class TestLargePopulationPerformance:
    
    @pytest.mark.performance
    def test_million_individual_simulation(self):
        """测试100万个体模拟性能"""
        
        # 创建100万个体人群
        population_size = 1000000
        population = Population.generate_random(size=population_size)
        
        # 记录开始时间和内存
        start_time = time.time()
        process = psutil.Process(os.getpid())
        start_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 运行模拟
        engine = MicrosimulationEngine()
        engine.initialize_population(population)
        results = engine.run_simulation(duration_years=10)
        
        # 记录结束时间和内存
        end_time = time.time()
        end_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 性能断言
        execution_time = end_time - start_time
        memory_usage = end_memory - start_memory
        
        assert execution_time < 1800  # 30分钟内完成
        assert memory_usage < 8192    # 内存使用小于8GB
        assert results is not None
        assert len(results.individual_outcomes) == 1000000
```
