# 结直肠癌筛查微观模拟模型

一个用于评估不同筛查策略成本效益的微观模拟模型，支持多种筛查工具和策略的比较分析。

## 项目概述

本项目是一个基于Python的桌面应用程序，使用PyQt6构建用户界面，集成了先进的数值计算和机器学习技术，用于：

- 模拟个体层面的疾病进展过程
- 评估不同筛查策略的效果
- 进行成本效益分析
- 生成专业的分析报告

## 技术栈

- **Python 3.9+**: 主要开发语言
- **PyQt6**: 跨平台桌面应用框架
- **NumPy & SciPy**: 高性能数值计算
- **Pandas**: 数据处理和分析
- **TensorFlow**: 深度学习模型校准
- **SQLAlchemy**: 数据库ORM
- **pytest**: 测试框架

## 快速开始

### 环境要求

- Python 3.9 或更高版本
- Poetry 1.5+ (推荐的依赖管理工具)
- Git

### 安装步骤

1. **克隆仓库**
   ```bash
   git clone <repository-url>
   cd colorectal-cancer-screening-model
   ```

2. **安装Poetry** (如果尚未安装)
   ```bash
   curl -sSL https://install.python-poetry.org | python3 -
   ```

3. **安装依赖**
   ```bash
   poetry install
   ```

4. **激活虚拟环境**
   ```bash
   poetry shell
   ```

5. **运行测试**
   ```bash
   pytest
   ```

### 开发环境设置

1. **安装开发依赖**
   ```bash
   poetry install --with dev,test
   ```

2. **设置pre-commit钩子**
   ```bash
   pre-commit install
   ```

3. **运行代码质量检查**
   ```bash
   # 代码格式化
   black src/ tests/
   
   # 导入排序
   isort src/ tests/
   
   # 类型检查
   mypy src/
   
   # 运行所有测试
   pytest --cov=src
   ```

## 项目结构

```
src/
├── core/                   # 核心模拟引擎
├── modules/                # 功能模块
├── interfaces/             # 用户界面
├── services/               # 服务层
├── database/               # 数据库层
└── utils/                  # 工具函数

tests/
├── unit/                   # 单元测试
├── integration/            # 集成测试
├── performance/            # 性能测试
└── fixtures/               # 测试数据
```

## 使用Docker

### 开发环境

```bash
# 构建开发环境
docker-compose up -d

# 进入容器
docker-compose exec app bash
```

### 生产环境

```bash
# 构建生产镜像
docker build -t colorectal-model .

# 运行容器
docker run -p 8080:8080 colorectal-model
```

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 开启 Pull Request

### 代码规范

- 使用Black进行代码格式化
- 遵循PEP 8编码规范
- 编写类型注解
- 保持测试覆盖率在90%以上

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目维护者: Development Team
- 邮箱: <EMAIL>
- 项目链接: [GitHub Repository](https://github.com/example/colorectal-cancer-screening-model)

## 更新日志

### v0.1.0 (2025-07-31)
- 初始项目结构搭建
- 基础开发环境配置
- 核心依赖安装和配置
