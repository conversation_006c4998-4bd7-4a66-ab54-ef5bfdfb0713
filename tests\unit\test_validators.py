"""
数据验证器单元测试

测试validators模块中的所有验证函数和异常类。
"""

import pytest
from unittest.mock import patch

from src.utils.validators import (
    ValidationError, AgeValidationError, GenderValidationError,
    DateValidationError, StateTransitionError, ParameterValidationError,
    validate_age, validate_birth_year, validate_gender, validate_disease_state,
    validate_pathway_type, validate_cancer_stage, validate_state_transition,
    validate_probability, validate_positive_number, validate_individual_id,
    validate_enum_value
)
from src.core.enums import DiseaseState, Gender, PathwayType, CancerStage


class TestValidationExceptions:
    """测试验证异常类"""
    
    def test_validation_error_basic(self):
        """测试基本验证错误"""
        error = ValidationError("测试错误")
        assert str(error) == "验证错误: 测试错误"
        assert error.message == "测试错误"
        assert error.field_name is None
        assert error.value is None
    
    def test_validation_error_with_field(self):
        """测试带字段名的验证错误"""
        error = ValidationError("测试错误", "test_field", "test_value")
        assert str(error) == "验证错误 [test_field]: 测试错误"
        assert error.field_name == "test_field"
        assert error.value == "test_value"
    
    def test_specific_validation_errors(self):
        """测试特定验证错误类型"""
        age_error = AgeValidationError("年龄错误", "age", -1)
        assert isinstance(age_error, ValidationError)
        
        gender_error = GenderValidationError("性别错误")
        assert isinstance(gender_error, ValidationError)
        
        date_error = DateValidationError("日期错误")
        assert isinstance(date_error, ValidationError)
        
        state_error = StateTransitionError("状态转换错误")
        assert isinstance(state_error, ValidationError)
        
        param_error = ParameterValidationError("参数错误")
        assert isinstance(param_error, ValidationError)


class TestAgeValidation:
    """测试年龄验证"""
    
    def test_validate_age_valid(self):
        """测试有效年龄"""
        assert validate_age(25) == 25.0
        assert validate_age(25.5) == 25.5
        assert validate_age("30") == 30.0
        assert validate_age(0) == 0.0
        assert validate_age(120) == 120.0
    
    def test_validate_age_invalid_type(self):
        """测试无效年龄类型"""
        with pytest.raises(AgeValidationError, match="年龄必须是数字类型"):
            validate_age("invalid")
        
        with pytest.raises(AgeValidationError, match="年龄必须是数字类型"):
            validate_age(None)
    
    def test_validate_age_negative(self):
        """测试负年龄"""
        with pytest.raises(AgeValidationError, match="年龄不能为负数"):
            validate_age(-1)
        
        with pytest.raises(AgeValidationError, match="年龄不能为负数"):
            validate_age(-0.1)
    
    def test_validate_age_too_old(self):
        """测试年龄过大"""
        with pytest.raises(AgeValidationError, match="年龄不能超过120岁"):
            validate_age(121)
        
        with pytest.raises(AgeValidationError, match="年龄不能超过120岁"):
            validate_age(150)


class TestBirthYearValidation:
    """测试出生年份验证"""
    
    @patch('src.utils.validators.datetime')
    def test_validate_birth_year_valid(self, mock_datetime):
        """测试有效出生年份"""
        mock_datetime.now.return_value.year = 2025
        
        assert validate_birth_year(1980) == 1980
        assert validate_birth_year(2025) == 2025
        assert validate_birth_year(1900) == 1900
    
    def test_validate_birth_year_invalid_type(self):
        """测试无效出生年份类型"""
        with pytest.raises(DateValidationError, match="出生年份必须是整数类型"):
            validate_birth_year("invalid")
        
        with pytest.raises(DateValidationError, match="出生年份必须是整数类型"):
            validate_birth_year(1980.5)
    
    def test_validate_birth_year_too_early(self):
        """测试出生年份过早"""
        with pytest.raises(DateValidationError, match="出生年份不能早于1900年"):
            validate_birth_year(1800)
    
    @patch('src.utils.validators.datetime')
    def test_validate_birth_year_too_late(self, mock_datetime):
        """测试出生年份过晚"""
        mock_datetime.now.return_value.year = 2025
        
        with pytest.raises(DateValidationError, match="出生年份不能晚于当前年份"):
            validate_birth_year(2030)


class TestGenderValidation:
    """测试性别验证"""
    
    def test_validate_gender_enum(self):
        """测试性别枚举验证"""
        assert validate_gender(Gender.MALE) == Gender.MALE
        assert validate_gender(Gender.FEMALE) == Gender.FEMALE
    
    def test_validate_gender_string(self):
        """测试性别字符串验证"""
        assert validate_gender("male") == Gender.MALE
        assert validate_gender("female") == Gender.FEMALE
        assert validate_gender("MALE") == Gender.MALE
        assert validate_gender("Female") == Gender.FEMALE
    
    def test_validate_gender_invalid_string(self):
        """测试无效性别字符串"""
        with pytest.raises(GenderValidationError, match="无效的性别值"):
            validate_gender("invalid")
        
        with pytest.raises(GenderValidationError, match="无效的性别值"):
            validate_gender("other")
    
    def test_validate_gender_invalid_type(self):
        """测试无效性别类型"""
        with pytest.raises(GenderValidationError, match="性别必须是字符串或Gender枚举"):
            validate_gender(123)
        
        with pytest.raises(GenderValidationError, match="性别必须是字符串或Gender枚举"):
            validate_gender(None)


class TestDiseaseStateValidation:
    """测试疾病状态验证"""
    
    def test_validate_disease_state_enum(self):
        """测试疾病状态枚举验证"""
        assert validate_disease_state(DiseaseState.NORMAL) == DiseaseState.NORMAL
        assert validate_disease_state(DiseaseState.LOW_RISK_ADENOMA) == DiseaseState.LOW_RISK_ADENOMA
    
    def test_validate_disease_state_string(self):
        """测试疾病状态字符串验证"""
        assert validate_disease_state("normal") == DiseaseState.NORMAL
        assert validate_disease_state("low_risk_adenoma") == DiseaseState.LOW_RISK_ADENOMA
    
    def test_validate_disease_state_invalid(self):
        """测试无效疾病状态"""
        with pytest.raises(ValidationError, match="无效的疾病状态"):
            validate_disease_state("invalid_state")
        
        with pytest.raises(ValidationError, match="疾病状态必须是字符串或DiseaseState枚举"):
            validate_disease_state(123)


class TestPathwayTypeValidation:
    """测试疾病通路类型验证"""
    
    def test_validate_pathway_type_enum(self):
        """测试通路类型枚举验证"""
        assert validate_pathway_type(PathwayType.ADENOMA_CARCINOMA) == PathwayType.ADENOMA_CARCINOMA
        assert validate_pathway_type(PathwayType.SERRATED_ADENOMA) == PathwayType.SERRATED_ADENOMA
    
    def test_validate_pathway_type_string(self):
        """测试通路类型字符串验证"""
        assert validate_pathway_type("adenoma_carcinoma") == PathwayType.ADENOMA_CARCINOMA
        assert validate_pathway_type("serrated_adenoma") == PathwayType.SERRATED_ADENOMA
    
    def test_validate_pathway_type_none(self):
        """测试None通路类型"""
        assert validate_pathway_type(None) is None
    
    def test_validate_pathway_type_invalid(self):
        """测试无效通路类型"""
        with pytest.raises(ValidationError, match="无效的通路类型"):
            validate_pathway_type("invalid_pathway")
        
        with pytest.raises(ValidationError, match="通路类型必须是字符串"):
            validate_pathway_type(123)


class TestCancerStageValidation:
    """测试癌症分期验证"""
    
    def test_validate_cancer_stage_enum(self):
        """测试癌症分期枚举验证"""
        assert validate_cancer_stage(CancerStage.STAGE_I) == CancerStage.STAGE_I
        assert validate_cancer_stage(CancerStage.STAGE_IV) == CancerStage.STAGE_IV
    
    def test_validate_cancer_stage_string(self):
        """测试癌症分期字符串验证"""
        assert validate_cancer_stage("stage_i") == CancerStage.STAGE_I
        assert validate_cancer_stage("stage_iv") == CancerStage.STAGE_IV
    
    def test_validate_cancer_stage_none(self):
        """测试None癌症分期"""
        assert validate_cancer_stage(None) is None
    
    def test_validate_cancer_stage_invalid(self):
        """测试无效癌症分期"""
        with pytest.raises(ValidationError, match="无效的癌症分期"):
            validate_cancer_stage("invalid_stage")
        
        with pytest.raises(ValidationError, match="癌症分期必须是字符串"):
            validate_cancer_stage(123)


class TestStateTransitionValidation:
    """测试状态转换验证"""
    
    def test_validate_state_transition_valid(self):
        """测试有效状态转换"""
        # 正常 -> 低风险腺瘤
        assert validate_state_transition(
            DiseaseState.NORMAL,
            DiseaseState.LOW_RISK_ADENOMA
        ) is True
        
        # 高风险腺瘤 -> 临床前癌症
        assert validate_state_transition(
            DiseaseState.HIGH_RISK_ADENOMA,
            DiseaseState.PRECLINICAL_CANCER
        ) is True
    
    def test_validate_state_transition_from_death(self):
        """测试从死亡状态转换"""
        with pytest.raises(StateTransitionError, match="死亡状态.*不能转换到其他状态"):
            validate_state_transition(
                DiseaseState.DEATH_OTHER,
                DiseaseState.NORMAL
            )
    
    def test_validate_state_transition_invalid(self):
        """测试无效状态转换"""
        with pytest.raises(StateTransitionError, match="无效的状态转换"):
            validate_state_transition(
                DiseaseState.NORMAL,
                DiseaseState.CLINICAL_CANCER
            )
    
    def test_validate_state_transition_pathway_incompatible(self):
        """测试通路不兼容的状态转换"""
        with pytest.raises(StateTransitionError, match="不兼容"):
            validate_state_transition(
                DiseaseState.NORMAL,
                DiseaseState.SMALL_SERRATED,
                PathwayType.ADENOMA_CARCINOMA
            )


class TestProbabilityValidation:
    """测试概率验证"""
    
    def test_validate_probability_valid(self):
        """测试有效概率"""
        assert validate_probability(0.0) == 0.0
        assert validate_probability(0.5) == 0.5
        assert validate_probability(1.0) == 1.0
        assert validate_probability("0.7") == 0.7
    
    def test_validate_probability_invalid_type(self):
        """测试无效概率类型"""
        with pytest.raises(ParameterValidationError, match="概率必须是数字类型"):
            validate_probability("invalid")
        
        with pytest.raises(ParameterValidationError, match="概率必须是数字类型"):
            validate_probability(None)
    
    def test_validate_probability_out_of_range(self):
        """测试概率超出范围"""
        with pytest.raises(ParameterValidationError, match="概率必须在0-1之间"):
            validate_probability(-0.1)
        
        with pytest.raises(ParameterValidationError, match="概率必须在0-1之间"):
            validate_probability(1.1)


class TestPositiveNumberValidation:
    """测试正数验证"""
    
    def test_validate_positive_number_valid(self):
        """测试有效正数"""
        assert validate_positive_number(1) == 1.0
        assert validate_positive_number(5.5) == 5.5
        assert validate_positive_number("10") == 10.0
    
    def test_validate_positive_number_with_zero(self):
        """测试允许零的正数验证"""
        assert validate_positive_number(0, allow_zero=True) == 0.0
        
        with pytest.raises(ParameterValidationError, match="数值必须大于0"):
            validate_positive_number(0, allow_zero=False)
    
    def test_validate_positive_number_negative(self):
        """测试负数"""
        with pytest.raises(ParameterValidationError, match="数值不能为负数"):
            validate_positive_number(-1, allow_zero=True)
        
        with pytest.raises(ParameterValidationError, match="数值必须大于0"):
            validate_positive_number(-1, allow_zero=False)
    
    def test_validate_positive_number_invalid_type(self):
        """测试无效数值类型"""
        with pytest.raises(ParameterValidationError, match="数值必须是数字类型"):
            validate_positive_number("invalid")


class TestIndividualIdValidation:
    """测试个体ID验证"""
    
    def test_validate_individual_id_valid(self):
        """测试有效个体ID"""
        # UUID格式
        uuid_id = "550e8400-e29b-41d4-a716-************"
        assert validate_individual_id(uuid_id) == uuid_id
        
        # 普通字符串
        simple_id = "individual-123"
        assert validate_individual_id(simple_id) == simple_id
    
    def test_validate_individual_id_with_whitespace(self):
        """测试带空白字符的个体ID"""
        id_with_spaces = "  individual-123  "
        assert validate_individual_id(id_with_spaces) == "individual-123"
    
    def test_validate_individual_id_invalid_type(self):
        """测试无效个体ID类型"""
        with pytest.raises(ValidationError, match="个体ID必须是字符串类型"):
            validate_individual_id(123)
        
        with pytest.raises(ValidationError, match="个体ID必须是字符串类型"):
            validate_individual_id(None)
    
    def test_validate_individual_id_empty(self):
        """测试空个体ID"""
        with pytest.raises(ValidationError, match="个体ID不能为空"):
            validate_individual_id("")
        
        with pytest.raises(ValidationError, match="个体ID不能为空"):
            validate_individual_id("   ")
    
    def test_validate_individual_id_too_short(self):
        """测试过短的个体ID"""
        with pytest.raises(ValidationError, match="个体ID长度至少为3个字符"):
            validate_individual_id("ab")


class TestEnumValueValidation:
    """测试枚举值验证"""
    
    def test_validate_enum_value_enum(self):
        """测试枚举值验证"""
        assert validate_enum_value(Gender.MALE, Gender) == Gender.MALE
        assert validate_enum_value(DiseaseState.NORMAL, DiseaseState) == DiseaseState.NORMAL
    
    def test_validate_enum_value_string(self):
        """测试枚举字符串值验证"""
        assert validate_enum_value("male", Gender) == Gender.MALE
        assert validate_enum_value("normal", DiseaseState) == DiseaseState.NORMAL
    
    def test_validate_enum_value_invalid_string(self):
        """测试无效枚举字符串值"""
        with pytest.raises(ValidationError, match="无效的Gender值"):
            validate_enum_value("invalid", Gender)
    
    def test_validate_enum_value_invalid_type(self):
        """测试无效枚举值类型"""
        with pytest.raises(ValidationError, match="Gender必须是字符串或Gender枚举"):
            validate_enum_value(123, Gender)
