"""
测试基础设施搭建的完整性

验证项目的基础设施配置是否正确完成。
"""

import pytest
import toml
from pathlib import Path


class TestInfrastructureSetup:
    """测试基础设施搭建的各个方面"""

    def test_pyproject_toml_configuration(self):
        """测试pyproject.toml配置是否正确"""
        project_root = Path(__file__).parent.parent.parent
        pyproject_path = project_root / "pyproject.toml"
        
        assert pyproject_path.exists(), "pyproject.toml文件应该存在"
        
        # 读取配置
        config = toml.load(pyproject_path)
        
        # 验证基本信息
        assert "tool" in config, "应该包含tool配置"
        assert "poetry" in config["tool"], "应该包含poetry配置"
        
        poetry_config = config["tool"]["poetry"]
        assert poetry_config["name"] == "colorectal-cancer-screening-model"
        assert poetry_config["version"] == "0.1.0"
        
        # 验证核心依赖
        dependencies = poetry_config["dependencies"]
        required_deps = ["numpy", "scipy", "pandas", "PyQt6", "matplotlib", 
                        "plotly", "SQLAlchemy", "PyYAML", "tensorflow"]
        
        for dep in required_deps:
            assert dep in dependencies, f"应该包含{dep}依赖"

    def test_git_configuration_files(self):
        """测试Git配置文件是否存在"""
        project_root = Path(__file__).parent.parent.parent
        
        git_files = [".gitignore", ".gitmessage", ".pre-commit-config.yaml"]
        
        for git_file in git_files:
            file_path = project_root / git_file
            assert file_path.exists(), f"{git_file}文件应该存在"

    def test_docker_configuration_files(self):
        """测试Docker配置文件是否存在"""
        project_root = Path(__file__).parent.parent.parent
        
        docker_files = ["Dockerfile.dev", "docker-compose.yml", ".dockerignore"]
        
        for docker_file in docker_files:
            file_path = project_root / docker_file
            assert file_path.exists(), f"{docker_file}文件应该存在"

    def test_github_workflows(self):
        """测试GitHub Actions工作流配置"""
        project_root = Path(__file__).parent.parent.parent
        workflow_path = project_root / ".github" / "workflows" / "ci.yml"
        
        assert workflow_path.exists(), "CI工作流文件应该存在"

    def test_readme_exists(self):
        """测试README文件是否存在且包含必要信息"""
        project_root = Path(__file__).parent.parent.parent
        readme_path = project_root / "README.md"
        
        assert readme_path.exists(), "README.md文件应该存在"
        
        readme_content = readme_path.read_text(encoding="utf-8")
        required_sections = [
            "结直肠癌筛查微观模拟模型",
            "项目概述",
            "技术栈",
            "快速开始",
            "项目结构"
        ]
        
        for section in required_sections:
            assert section in readme_content, f"README应该包含{section}部分"

    def test_code_quality_tools_configuration(self):
        """测试代码质量工具配置"""
        project_root = Path(__file__).parent.parent.parent
        pyproject_path = project_root / "pyproject.toml"
        
        config = toml.load(pyproject_path)
        
        # 验证Black配置
        assert "black" in config["tool"], "应该包含Black配置"
        black_config = config["tool"]["black"]
        assert black_config["line-length"] == 88
        
        # 验证isort配置
        assert "isort" in config["tool"], "应该包含isort配置"
        isort_config = config["tool"]["isort"]
        assert isort_config["profile"] == "black"
        
        # 验证mypy配置
        assert "mypy" in config["tool"], "应该包含mypy配置"
        mypy_config = config["tool"]["mypy"]
        assert mypy_config["python_version"] == "3.9"
        
        # 验证pytest配置
        assert "pytest" in config["tool"], "应该包含pytest配置"
        pytest_config = config["tool"]["pytest"]["ini_options"]
        assert "tests" in pytest_config["testpaths"]

    def test_development_dependencies(self):
        """测试开发依赖是否正确配置"""
        project_root = Path(__file__).parent.parent.parent
        pyproject_path = project_root / "pyproject.toml"
        
        config = toml.load(pyproject_path)
        poetry_config = config["tool"]["poetry"]
        
        # 验证开发依赖组
        assert "group" in poetry_config, "应该包含依赖组配置"
        assert "dev" in poetry_config["group"], "应该包含dev依赖组"
        
        dev_deps = poetry_config["group"]["dev"]["dependencies"]
        required_dev_deps = ["pytest", "black", "isort", "mypy", "pre-commit"]
        
        for dep in required_dev_deps:
            assert dep in dev_deps, f"开发依赖应该包含{dep}"
