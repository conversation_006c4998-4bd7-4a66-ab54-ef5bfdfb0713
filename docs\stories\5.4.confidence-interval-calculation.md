# Story 5.4: 置信区间计算

## Status
Draft

## Story
**As a** 统计学家，
**I want** 计算校准结果的95%置信区间，
**so that** 量化参数估计的不确定性。

## Acceptance Criteria
1. 实现Bootstrap方法的置信区间计算
2. 创建参数不确定性的传播分析
3. 实现置信区间的可视化展示
4. 添加置信区间覆盖率的验证测试
5. 创建不确定性分析报告生成功能
6. 实现置信区间计算的并行化处理

## Tasks / Subtasks

- [ ] 任务1：实现Bootstrap置信区间计算 (AC: 1)
  - [ ] 创建src/calibration/confidence_intervals.py文件
  - [ ] 实现BootstrapCI类，计算Bootstrap置信区间
  - [ ] 添加多种Bootstrap方法支持（非参数、参数、偏差校正）
  - [ ] 实现Bootstrap样本的生成和管理
  - [ ] 创建置信区间的多种计算方法（百分位数、BCa等）
  - [ ] 添加Bootstrap收敛性检验功能

- [ ] 任务2：创建参数不确定性传播分析 (AC: 2)
  - [ ] 创建src/calibration/uncertainty_propagation.py文件
  - [ ] 实现UncertaintyPropagator类，分析不确定性传播
  - [ ] 添加参数相关性对不确定性的影响分析
  - [ ] 实现蒙特卡洛不确定性传播方法
  - [ ] 创建敏感性分析与不确定性的结合
  - [ ] 添加不确定性来源的分解和量化

- [ ] 任务3：实现置信区间可视化展示 (AC: 3)
  - [ ] 创建src/interfaces/desktop/widgets/confidence_interval_widget.py
  - [ ] 实现ConfidenceIntervalWidget类，可视化置信区间
  - [ ] 添加置信区间带状图和误差条图
  - [ ] 创建参数不确定性的热图可视化
  - [ ] 实现交互式置信区间调整功能
  - [ ] 添加置信区间覆盖概率的可视化

- [ ] 任务4：添加置信区间覆盖率验证 (AC: 4)
  - [ ] 创建src/calibration/coverage_validation.py文件
  - [ ] 实现CoverageValidator类，验证覆盖率
  - [ ] 添加模拟验证置信区间覆盖率功能
  - [ ] 实现覆盖率的统计检验
  - [ ] 创建覆盖率诊断和改进建议
  - [ ] 添加覆盖率基准测试套件

- [ ] 任务5：创建不确定性分析报告 (AC: 5)
  - [ ] 创建src/services/uncertainty_report_generator.py文件
  - [ ] 实现UncertaintyReportGenerator类，生成分析报告
  - [ ] 添加不确定性分析的标准化报告模板
  - [ ] 实现报告的自动化内容生成
  - [ ] 创建不确定性可视化的自动嵌入
  - [ ] 添加报告的多格式导出功能

- [ ] 任务6：实现并行化处理优化 (AC: 6)
  - [ ] 优化Bootstrap计算的并行处理
  - [ ] 实现多进程和多线程的混合并行
  - [ ] 添加内存高效的大规模Bootstrap处理
  - [ ] 创建分布式计算支持（可选）
  - [ ] 实现计算进度监控和中断恢复
  - [ ] 添加并行计算的性能基准测试

## Dev Notes

### 置信区间计算数据结构
```python
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Callable, Any
import numpy as np
from scipy import stats
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import multiprocessing as mp

@dataclass
class ConfidenceInterval:
    parameter_name: str
    point_estimate: float
    lower_bound: float
    upper_bound: float
    confidence_level: float
    method: str
    bootstrap_samples: Optional[np.ndarray] = None
    standard_error: Optional[float] = None

@dataclass
class UncertaintyAnalysis:
    parameter_cis: Dict[str, ConfidenceInterval]
    correlation_matrix: np.ndarray
    parameter_names: List[str]
    total_uncertainty: float
    uncertainty_sources: Dict[str, float]
    coverage_validation: Optional[Dict[str, float]] = None
```

### Bootstrap置信区间实现
```python
class BootstrapCI:
    def __init__(self, n_bootstrap: int = 1000, confidence_level: float = 0.95):
        self.n_bootstrap = n_bootstrap
        self.confidence_level = confidence_level
        self.alpha = 1 - confidence_level
        
    def calculate_ci(
        self, 
        data: np.ndarray, 
        statistic_func: Callable,
        method: str = "percentile"
    ) -> ConfidenceInterval:
        """计算Bootstrap置信区间"""
        
        # 原始统计量
        original_stat = statistic_func(data)
        
        # Bootstrap重抽样
        bootstrap_stats = self._bootstrap_resample(data, statistic_func)
        
        # 计算置信区间
        if method == "percentile":
            ci = self._percentile_ci(bootstrap_stats)
        elif method == "bca":
            ci = self._bca_ci(data, statistic_func, bootstrap_stats, original_stat)
        elif method == "basic":
            ci = self._basic_ci(bootstrap_stats, original_stat)
        else:
            raise ValueError(f"Unknown CI method: {method}")
        
        # 计算标准误差
        standard_error = np.std(bootstrap_stats, ddof=1)
        
        return ConfidenceInterval(
            parameter_name="parameter",
            point_estimate=original_stat,
            lower_bound=ci[0],
            upper_bound=ci[1],
            confidence_level=self.confidence_level,
            method=method,
            bootstrap_samples=bootstrap_stats,
            standard_error=standard_error
        )
    
    def _bootstrap_resample(
        self, 
        data: np.ndarray, 
        statistic_func: Callable
    ) -> np.ndarray:
        """Bootstrap重抽样"""
        
        n = len(data)
        bootstrap_stats = np.zeros(self.n_bootstrap)
        
        # 使用并行处理加速
        if self.n_bootstrap > 100:
            bootstrap_stats = self._parallel_bootstrap(data, statistic_func)
        else:
            for i in range(self.n_bootstrap):
                # 有放回抽样
                bootstrap_sample = np.random.choice(data, size=n, replace=True)
                bootstrap_stats[i] = statistic_func(bootstrap_sample)
        
        return bootstrap_stats
    
    def _parallel_bootstrap(
        self, 
        data: np.ndarray, 
        statistic_func: Callable
    ) -> np.ndarray:
        """并行Bootstrap重抽样"""
        
        def bootstrap_worker(args):
            data, statistic_func, n_samples, seed = args
            np.random.seed(seed)
            n = len(data)
            results = []
            
            for _ in range(n_samples):
                bootstrap_sample = np.random.choice(data, size=n, replace=True)
                results.append(statistic_func(bootstrap_sample))
            
            return results
        
        # 分割任务
        n_workers = min(mp.cpu_count(), 8)
        samples_per_worker = self.n_bootstrap // n_workers
        remaining_samples = self.n_bootstrap % n_workers
        
        tasks = []
        for i in range(n_workers):
            n_samples = samples_per_worker + (1 if i < remaining_samples else 0)
            seed = np.random.randint(0, 2**32 - 1)
            tasks.append((data, statistic_func, n_samples, seed))
        
        # 并行执行
        with ProcessPoolExecutor(max_workers=n_workers) as executor:
            results = executor.map(bootstrap_worker, tasks)
        
        # 合并结果
        bootstrap_stats = []
        for result in results:
            bootstrap_stats.extend(result)
        
        return np.array(bootstrap_stats)
    
    def _percentile_ci(self, bootstrap_stats: np.ndarray) -> Tuple[float, float]:
        """百分位数置信区间"""
        lower_percentile = (self.alpha / 2) * 100
        upper_percentile = (1 - self.alpha / 2) * 100
        
        lower_bound = np.percentile(bootstrap_stats, lower_percentile)
        upper_bound = np.percentile(bootstrap_stats, upper_percentile)
        
        return lower_bound, upper_bound
    
    def _bca_ci(
        self, 
        data: np.ndarray, 
        statistic_func: Callable,
        bootstrap_stats: np.ndarray, 
        original_stat: float
    ) -> Tuple[float, float]:
        """偏差校正和加速(BCa)置信区间"""
        
        # 计算偏差校正
        n_less = np.sum(bootstrap_stats < original_stat)
        bias_correction = stats.norm.ppf(n_less / self.n_bootstrap)
        
        # 计算加速度参数
        n = len(data)
        jackknife_stats = np.zeros(n)
        
        for i in range(n):
            # Jackknife样本（删除第i个观测）
            jackknife_sample = np.delete(data, i)
            jackknife_stats[i] = statistic_func(jackknife_sample)
        
        jackknife_mean = np.mean(jackknife_stats)
        numerator = np.sum((jackknife_mean - jackknife_stats) ** 3)
        denominator = 6 * (np.sum((jackknife_mean - jackknife_stats) ** 2) ** 1.5)
        
        if denominator != 0:
            acceleration = numerator / denominator
        else:
            acceleration = 0
        
        # 计算调整后的分位数
        z_alpha_2 = stats.norm.ppf(self.alpha / 2)
        z_1_alpha_2 = stats.norm.ppf(1 - self.alpha / 2)
        
        alpha_1 = stats.norm.cdf(bias_correction + 
                                (bias_correction + z_alpha_2) / 
                                (1 - acceleration * (bias_correction + z_alpha_2)))
        alpha_2 = stats.norm.cdf(bias_correction + 
                                (bias_correction + z_1_alpha_2) / 
                                (1 - acceleration * (bias_correction + z_1_alpha_2)))
        
        lower_bound = np.percentile(bootstrap_stats, alpha_1 * 100)
        upper_bound = np.percentile(bootstrap_stats, alpha_2 * 100)
        
        return lower_bound, upper_bound
```

### 不确定性传播分析
```python
class UncertaintyPropagator:
    def __init__(self, model_function: Callable):
        self.model_function = model_function
        
    def propagate_uncertainty(
        self, 
        parameter_distributions: Dict[str, stats.rv_continuous],
        n_samples: int = 10000
    ) -> UncertaintyAnalysis:
        """传播参数不确定性"""
        
        parameter_names = list(parameter_distributions.keys())
        n_params = len(parameter_names)
        
        # 生成参数样本
        parameter_samples = np.zeros((n_samples, n_params))
        for i, (param_name, distribution) in enumerate(parameter_distributions.items()):
            parameter_samples[:, i] = distribution.rvs(n_samples)
        
        # 运行模型获取输出
        model_outputs = []
        for i in range(n_samples):
            params = dict(zip(parameter_names, parameter_samples[i]))
            output = self.model_function(params)
            model_outputs.append(output)
        
        model_outputs = np.array(model_outputs)
        
        # 计算每个参数的置信区间
        parameter_cis = {}
        bootstrap_ci = BootstrapCI()
        
        for i, param_name in enumerate(parameter_names):
            param_samples = parameter_samples[:, i]
            ci = bootstrap_ci.calculate_ci(param_samples, np.mean)
            ci.parameter_name = param_name
            parameter_cis[param_name] = ci
        
        # 计算参数相关性
        correlation_matrix = np.corrcoef(parameter_samples.T)
        
        # 分析不确定性来源
        uncertainty_sources = self._analyze_uncertainty_sources(
            parameter_samples, model_outputs, parameter_names
        )
        
        # 计算总不确定性
        total_uncertainty = np.std(model_outputs)
        
        return UncertaintyAnalysis(
            parameter_cis=parameter_cis,
            correlation_matrix=correlation_matrix,
            parameter_names=parameter_names,
            total_uncertainty=total_uncertainty,
            uncertainty_sources=uncertainty_sources
        )
    
    def _analyze_uncertainty_sources(
        self, 
        parameter_samples: np.ndarray,
        model_outputs: np.ndarray,
        parameter_names: List[str]
    ) -> Dict[str, float]:
        """分析不确定性来源"""
        
        uncertainty_sources = {}
        total_variance = np.var(model_outputs)
        
        # 计算每个参数对总不确定性的贡献
        for i, param_name in enumerate(parameter_names):
            # 使用Sobol指数或相关性分析
            param_values = parameter_samples[:, i]
            
            # 计算一阶敏感性指数
            correlation = np.corrcoef(param_values, model_outputs)[0, 1]
            contribution = (correlation ** 2) * total_variance
            
            uncertainty_sources[param_name] = contribution / total_variance
        
        # 添加交互效应
        interaction_variance = total_variance - sum(uncertainty_sources.values()) * total_variance
        uncertainty_sources['interactions'] = max(0, interaction_variance / total_variance)
        
        return uncertainty_sources
```

### 覆盖率验证
```python
class CoverageValidator:
    def __init__(self):
        self.validation_results = {}
        
    def validate_coverage(
        self, 
        true_parameters: Dict[str, float],
        confidence_intervals: Dict[str, ConfidenceInterval],
        n_simulations: int = 1000
    ) -> Dict[str, float]:
        """验证置信区间覆盖率"""
        
        coverage_rates = {}
        
        for param_name, true_value in true_parameters.items():
            if param_name in confidence_intervals:
                ci = confidence_intervals[param_name]
                
                # 模拟验证
                coverage_count = 0
                
                for _ in range(n_simulations):
                    # 生成新的数据样本（基于真实参数）
                    simulated_data = self._simulate_data(true_value)
                    
                    # 计算置信区间
                    bootstrap_ci = BootstrapCI(confidence_level=ci.confidence_level)
                    simulated_ci = bootstrap_ci.calculate_ci(
                        simulated_data, 
                        np.mean
                    )
                    
                    # 检查是否覆盖真实值
                    if (simulated_ci.lower_bound <= true_value <= 
                        simulated_ci.upper_bound):
                        coverage_count += 1
                
                coverage_rate = coverage_count / n_simulations
                coverage_rates[param_name] = coverage_rate
        
        return coverage_rates
    
    def _simulate_data(self, true_parameter: float, n_samples: int = 100) -> np.ndarray:
        """模拟数据生成"""
        # 这里应该根据实际的数据生成过程来实现
        # 示例：假设数据来自正态分布
        return np.random.normal(true_parameter, 1.0, n_samples)
    
    def diagnose_coverage_issues(
        self, 
        coverage_rates: Dict[str, float],
        expected_coverage: float = 0.95
    ) -> Dict[str, str]:
        """诊断覆盖率问题"""
        
        diagnoses = {}
        tolerance = 0.05  # 5%的容忍度
        
        for param_name, coverage_rate in coverage_rates.items():
            if abs(coverage_rate - expected_coverage) > tolerance:
                if coverage_rate < expected_coverage - tolerance:
                    diagnoses[param_name] = (
                        f"覆盖率过低 ({coverage_rate:.3f})，"
                        f"可能需要增加Bootstrap样本数或使用BCa方法"
                    )
                elif coverage_rate > expected_coverage + tolerance:
                    diagnoses[param_name] = (
                        f"覆盖率过高 ({coverage_rate:.3f})，"
                        f"置信区间可能过于保守"
                    )
            else:
                diagnoses[param_name] = f"覆盖率正常 ({coverage_rate:.3f})"
        
        return diagnoses
```

### 不确定性报告生成
```python
class UncertaintyReportGenerator:
    def __init__(self):
        self.report_template = self._load_report_template()
        
    def generate_uncertainty_report(
        self, 
        uncertainty_analysis: UncertaintyAnalysis,
        coverage_validation: Optional[Dict[str, float]] = None
    ) -> Dict[str, Any]:
        """生成不确定性分析报告"""
        
        report = {
            'executive_summary': self._generate_executive_summary(uncertainty_analysis),
            'parameter_uncertainty': self._generate_parameter_section(uncertainty_analysis),
            'uncertainty_sources': self._generate_sources_section(uncertainty_analysis),
            'correlation_analysis': self._generate_correlation_section(uncertainty_analysis),
            'coverage_validation': self._generate_coverage_section(coverage_validation),
            'recommendations': self._generate_recommendations(uncertainty_analysis),
            'technical_details': self._generate_technical_section(uncertainty_analysis)
        }
        
        return report
    
    def _generate_executive_summary(self, analysis: UncertaintyAnalysis) -> str:
        """生成执行摘要"""
        
        # 找到不确定性最大的参数
        max_uncertainty_param = max(
            analysis.parameter_cis.items(),
            key=lambda x: (x[1].upper_bound - x[1].lower_bound) / abs(x[1].point_estimate)
        )
        
        # 主要不确定性来源
        main_source = max(analysis.uncertainty_sources.items(), key=lambda x: x[1])
        
        summary = f"""
        不确定性分析执行摘要
        
        本分析量化了{len(analysis.parameter_names)}个校准参数的不确定性。
        
        主要发现：
        - 总体不确定性水平：{analysis.total_uncertainty:.4f}
        - 不确定性最大的参数：{max_uncertainty_param[0]}
          (95%置信区间：{max_uncertainty_param[1].lower_bound:.4f} - 
           {max_uncertainty_param[1].upper_bound:.4f})
        - 主要不确定性来源：{main_source[0]} ({main_source[1]:.1%})
        
        建议：基于分析结果，建议重点关注{max_uncertainty_param[0]}参数的数据质量改进。
        """
        
        return summary.strip()
    
    def export_report(
        self, 
        report: Dict[str, Any], 
        output_path: str, 
        format: str = "pdf"
    ):
        """导出报告"""
        
        if format == "pdf":
            self._export_pdf_report(report, output_path)
        elif format == "html":
            self._export_html_report(report, output_path)
        elif format == "word":
            self._export_word_report(report, output_path)
        else:
            raise ValueError(f"Unsupported export format: {format}")
```

### Testing
#### 测试文件位置
- `tests/unit/test_bootstrap_ci.py`
- `tests/unit/test_uncertainty_propagation.py`
- `tests/unit/test_coverage_validation.py`
- `tests/integration/test_confidence_intervals.py`

#### 测试标准
- Bootstrap置信区间计算准确性测试
- 不确定性传播分析测试
- 覆盖率验证功能测试
- 并行计算性能测试
- 报告生成完整性测试

#### 测试框架和模式
- 使用已知分布验证置信区间准确性
- Monte Carlo模拟验证覆盖率
- 性能基准测试验证并行效率
- 集成测试验证完整分析流程

#### 特定测试要求
- 置信区间覆盖率: 95%CI实际覆盖率 94%-96%
- Bootstrap收敛性: 1000次重抽样结果稳定
- 并行计算效率: 多核加速比 > 3倍
- 内存使用: 大规模Bootstrap < 4GB内存

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-31 | 1.0 | 初始故事创建 | Scrum Master |

## Dev Agent Record
*此部分将由开发代理在实施过程中填写*

### Agent Model Used
*待填写*

### Debug Log References
*待填写*

### Completion Notes List
*待填写*

### File List
*待填写*

## QA Results
*此部分将由QA代理在完成故事实施的QA审查后填写*
