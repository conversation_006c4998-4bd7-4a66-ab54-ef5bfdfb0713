"""
Population类单元测试

测试Population类的个体管理、统计计算、筛选功能等。
"""

import pytest
from unittest.mock import patch

from src.core.population import Population, PopulationStatistics
from src.core.individual import Individual
from src.core.enums import DiseaseState, Gender, PathwayType, CancerStage


@pytest.fixture
def sample_individuals():
    """创建示例个体列表"""
    individuals = []
    
    # 创建不同年龄、性别、状态的个体
    for i in range(10):
        birth_year = 1970 + i * 2  # 1970, 1972, 1974, ...
        gender = Gender.MALE if i % 2 == 0 else Gender.FEMALE
        
        individual = Individual(
            birth_year=birth_year,
            gender=gender,
            individual_id=f"test-{i:03d}"
        )
        
        # 设置不同的疾病状态
        if i < 6:
            # 保持正常状态
            pass
        elif i < 8:
            # 设置为腺瘤状态
            individual.transition_to_state(DiseaseState.LOW_RISK_ADENOMA)
        else:
            # 设置为癌症状态
            individual.transition_to_state(DiseaseState.LOW_RISK_ADENOMA)
            individual.transition_to_state(DiseaseState.HIGH_RISK_ADENOMA)
            individual.transition_to_state(
                DiseaseState.PRECLINICAL_CANCER,
                cancer_stage=CancerStage.STAGE_I
            )
        
        individuals.append(individual)
    
    return individuals


class TestPopulationStatistics:
    """测试PopulationStatistics类"""
    
    def test_age_distribution_default_bins(self, sample_individuals):
        """测试默认年龄分组的年龄分布"""
        population = Population(sample_individuals)
        stats = population.statistics
        
        age_dist = stats.get_age_distribution()
        
        # 验证返回的是字典
        assert isinstance(age_dist, dict)
        
        # 验证所有个体都被计算在内
        total_count = sum(age_dist.values())
        assert total_count == len(sample_individuals)
    
    def test_age_distribution_custom_bins(self, sample_individuals):
        """测试自定义年龄分组"""
        population = Population(sample_individuals)
        stats = population.statistics
        
        custom_bins = [0, 30, 50, 70, 120]
        age_dist = stats.get_age_distribution(custom_bins)
        
        # 验证分组数量正确
        assert len(age_dist) == len(custom_bins) - 1
        
        # 验证键名格式
        expected_keys = ["0-30", "30-50", "50-70", "70-120"]
        assert set(age_dist.keys()) == set(expected_keys)
    
    def test_gender_distribution(self, sample_individuals):
        """测试性别分布"""
        population = Population(sample_individuals)
        stats = population.statistics
        
        gender_dist = stats.get_gender_distribution()
        
        # 验证包含男女两种性别
        assert "male" in gender_dist
        assert "female" in gender_dist
        
        # 验证总数正确
        total_count = sum(gender_dist.values())
        assert total_count == len(sample_individuals)
        
        # 验证男女比例（根据sample_individuals的创建逻辑）
        assert gender_dist["male"] == 5
        assert gender_dist["female"] == 5
    
    def test_disease_state_distribution(self, sample_individuals):
        """测试疾病状态分布"""
        population = Population(sample_individuals)
        stats = population.statistics
        
        disease_dist = stats.get_disease_state_distribution()
        
        # 验证包含预期的疾病状态
        assert "normal" in disease_dist
        assert "low_risk_adenoma" in disease_dist
        assert "preclinical_cancer" in disease_dist
        
        # 验证总数正确
        total_count = sum(disease_dist.values())
        assert total_count == len(sample_individuals)
    
    def test_cancer_stage_distribution(self, sample_individuals):
        """测试癌症分期分布"""
        population = Population(sample_individuals)
        stats = population.statistics
        
        stage_dist = stats.get_cancer_stage_distribution()
        
        # 验证只包含癌症患者的分期
        if stage_dist:
            assert "stage_i" in stage_dist
            # 癌症患者数量应该等于分期分布的总数
            cancer_count = sum(stage_dist.values())
            assert cancer_count == 2  # 根据sample_individuals的设置
    
    def test_survival_rate(self, sample_individuals):
        """测试存活率"""
        population = Population(sample_individuals)
        stats = population.statistics
        
        survival_rate = stats.get_survival_rate()
        
        # 所有示例个体都应该存活
        assert survival_rate == 1.0
    
    @patch('src.core.individual.datetime')
    def test_mean_age(self, mock_datetime, sample_individuals):
        """测试平均年龄计算"""
        mock_datetime.now.return_value.year = 2025
        
        population = Population(sample_individuals)
        stats = population.statistics
        
        mean_age = stats.get_mean_age()
        
        # 验证平均年龄在合理范围内
        assert 35 <= mean_age <= 65
    
    def test_summary(self, sample_individuals):
        """测试统计摘要"""
        population = Population(sample_individuals)
        stats = population.statistics
        
        summary = stats.get_summary()
        
        # 验证包含所有预期的统计信息
        expected_keys = [
            "total_individuals",
            "mean_age",
            "survival_rate",
            "age_distribution",
            "gender_distribution",
            "disease_state_distribution",
            "pathway_distribution",
            "cancer_stage_distribution",
        ]
        
        for key in expected_keys:
            assert key in summary
        
        # 验证个体总数
        assert summary["total_individuals"] == len(sample_individuals)


class TestPopulation:
    """测试Population类"""
    
    def test_population_creation_empty(self):
        """测试创建空人群"""
        population = Population()
        
        assert population.get_size() == 0
        assert population.is_empty() is True
        assert len(population) == 0
    
    def test_population_creation_with_individuals(self, sample_individuals):
        """测试使用个体列表创建人群"""
        population = Population(sample_individuals)
        
        assert population.get_size() == len(sample_individuals)
        assert population.is_empty() is False
        assert len(population) == len(sample_individuals)
    
    def test_add_individual(self):
        """测试添加个体"""
        population = Population()
        individual = Individual(birth_year=1980, gender=Gender.MALE)
        
        success = population.add_individual(individual)
        assert success is True
        assert population.get_size() == 1
        assert individual.individual_id in population
    
    def test_add_duplicate_individual(self):
        """测试添加重复个体"""
        population = Population()
        individual = Individual(
            birth_year=1980,
            gender=Gender.MALE,
            individual_id="duplicate-test"
        )
        
        # 第一次添加成功
        assert population.add_individual(individual) is True
        
        # 第二次添加失败
        assert population.add_individual(individual) is False
        assert population.get_size() == 1
    
    def test_remove_individual(self, sample_individuals):
        """测试移除个体"""
        population = Population(sample_individuals)
        initial_size = population.get_size()
        
        # 移除存在的个体
        individual_id = sample_individuals[0].individual_id
        success = population.remove_individual(individual_id)
        
        assert success is True
        assert population.get_size() == initial_size - 1
        assert individual_id not in population
    
    def test_remove_nonexistent_individual(self, sample_individuals):
        """测试移除不存在的个体"""
        population = Population(sample_individuals)
        initial_size = population.get_size()
        
        success = population.remove_individual("nonexistent-id")
        
        assert success is False
        assert population.get_size() == initial_size
    
    def test_get_individual(self, sample_individuals):
        """测试获取个体"""
        population = Population(sample_individuals)
        
        # 获取存在的个体
        individual_id = sample_individuals[0].individual_id
        individual = population.get_individual(individual_id)
        
        assert individual is not None
        assert individual.individual_id == individual_id
        
        # 获取不存在的个体
        nonexistent = population.get_individual("nonexistent-id")
        assert nonexistent is None
    
    def test_filter_by_gender(self, sample_individuals):
        """测试按性别筛选"""
        population = Population(sample_individuals)
        
        males = population.filter_by_gender(Gender.MALE)
        females = population.filter_by_gender(Gender.FEMALE)
        
        # 验证筛选结果
        assert len(males) == 5
        assert len(females) == 5
        
        # 验证所有男性个体的性别
        for individual in males:
            assert individual.gender == Gender.MALE
        
        # 验证所有女性个体的性别
        for individual in females:
            assert individual.gender == Gender.FEMALE
    
    def test_filter_by_disease_state(self, sample_individuals):
        """测试按疾病状态筛选"""
        population = Population(sample_individuals)
        
        normal_individuals = population.filter_by_disease_state(DiseaseState.NORMAL)
        cancer_individuals = population.filter_by_disease_state(DiseaseState.PRECLINICAL_CANCER)
        
        # 验证正常状态个体数量
        assert len(normal_individuals) == 6
        
        # 验证癌症个体数量
        assert len(cancer_individuals) == 2
        
        # 验证筛选结果的状态
        for individual in normal_individuals:
            assert individual.current_disease_state == DiseaseState.NORMAL
    
    @patch('src.core.individual.datetime')
    def test_filter_by_age_range(self, mock_datetime, sample_individuals):
        """测试按年龄范围筛选"""
        mock_datetime.now.return_value.year = 2025
        
        population = Population(sample_individuals)
        
        # 筛选40-50岁的个体
        middle_aged = population.filter_by_age_range(40, 50)
        
        # 验证年龄范围
        for individual in middle_aged:
            age = individual.get_current_age()
            assert 40 <= age <= 50
    
    def test_filter_by_criteria(self, sample_individuals):
        """测试按自定义条件筛选"""
        population = Population(sample_individuals)
        
        # 筛选男性且疾病状态为正常的个体
        criteria = lambda ind: (
            ind.gender == Gender.MALE and 
            ind.current_disease_state == DiseaseState.NORMAL
        )
        
        filtered = population.filter_by_criteria(criteria)
        
        # 验证筛选结果
        for individual in filtered:
            assert individual.gender == Gender.MALE
            assert individual.current_disease_state == DiseaseState.NORMAL
    
    def test_get_alive_individuals(self, sample_individuals):
        """测试获取存活个体"""
        population = Population(sample_individuals)
        
        # 设置一个个体为死亡状态
        sample_individuals[0].current_disease_state = DiseaseState.DEATH_OTHER
        
        alive_individuals = population.get_alive_individuals()
        
        # 验证存活个体数量
        assert len(alive_individuals) == len(sample_individuals) - 1
        
        # 验证所有返回的个体都存活
        for individual in alive_individuals:
            assert individual.is_alive()
    
    def test_get_cancer_patients(self, sample_individuals):
        """测试获取癌症患者"""
        population = Population(sample_individuals)
        
        cancer_patients = population.get_cancer_patients()
        
        # 验证癌症患者数量
        assert len(cancer_patients) == 2
        
        # 验证所有返回的个体都是癌症状态
        for individual in cancer_patients:
            assert individual.current_disease_state.is_cancer()
    
    def test_batch_transition_states(self, sample_individuals):
        """测试批量状态转换"""
        population = Population(sample_individuals)
        
        # 将所有正常状态的个体转换为低风险腺瘤
        criteria = lambda ind: ind.current_disease_state == DiseaseState.NORMAL
        count = population.batch_transition_states(
            criteria,
            DiseaseState.LOW_RISK_ADENOMA
        )
        
        # 验证转换数量
        assert count == 6  # 原本有6个正常状态的个体
        
        # 验证转换结果
        normal_individuals = population.filter_by_disease_state(DiseaseState.NORMAL)
        adenoma_individuals = population.filter_by_disease_state(DiseaseState.LOW_RISK_ADENOMA)
        
        assert len(normal_individuals) == 0
        assert len(adenoma_individuals) == 8  # 6个新转换 + 2个原有
    
    def test_clear(self, sample_individuals):
        """测试清空人群"""
        population = Population(sample_individuals)
        
        assert population.get_size() > 0
        
        population.clear()
        
        assert population.get_size() == 0
        assert population.is_empty() is True
    
    def test_iteration(self, sample_individuals):
        """测试迭代器功能"""
        population = Population(sample_individuals)
        
        count = 0
        for individual in population:
            assert isinstance(individual, Individual)
            count += 1
        
        assert count == len(sample_individuals)
    
    def test_contains(self, sample_individuals):
        """测试包含检查"""
        population = Population(sample_individuals)
        
        # 检查存在的个体
        individual_id = sample_individuals[0].individual_id
        assert individual_id in population
        
        # 检查不存在的个体
        assert "nonexistent-id" not in population
    
    def test_to_dict(self, sample_individuals):
        """测试转换为字典"""
        population = Population(sample_individuals)
        
        data = population.to_dict()
        
        # 验证字典结构
        assert "size" in data
        assert "statistics" in data
        assert "individuals" in data
        
        # 验证数据正确性
        assert data["size"] == len(sample_individuals)
        assert len(data["individuals"]) == len(sample_individuals)
    
    def test_repr(self, sample_individuals):
        """测试字符串表示"""
        population = Population(sample_individuals)
        
        repr_str = repr(population)
        assert "Population" in repr_str
        assert str(len(sample_individuals)) in repr_str
