# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.pytest_cache
nosetests.xml
coverage.xml
*.cover
*.log
.mypy_cache

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode
.idea
*.swp
*.swo

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
data/raw/
data/processed/
models/trained/
logs/
*.db
*.sqlite
*.sqlite3

# Documentation
docs/_build/

# Temporary files
tmp/
temp/
*.tmp

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# CI/CD
.github/

# Poetry
poetry.lock

# Jupyter
.ipynb_checkpoints

# AI/ML
*.h5
*.pkl
*.pickle
*.joblib
*.model
wandb/
mlruns/
