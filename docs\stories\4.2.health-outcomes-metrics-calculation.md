# Story 4.2: 健康结果指标计算

## Status
Draft

## Story
**As a** 卫生经济学家，
**I want** 计算标准化的健康结果指标，
**so that** 量化筛查策略的健康效益。

## Acceptance Criteria
1. 实现质量调整生命年（QALY）计算引擎
2. 计算挽救生命年（LYG）指标
3. 实现健康效用值的年龄和疾病状态调整
4. 添加生命质量权重的配置和管理
5. 创建健康结果的置信区间计算
6. 实现健康结果指标的验证测试

## Tasks / Subtasks

- [ ] 任务1：实现QALY计算引擎 (AC: 1)
  - [ ] 创建src/modules/economics/qaly_calculator.py文件
  - [ ] 实现QALYCalculator类，计算质量调整生命年
  - [ ] 添加健康状态特异性效用值配置
  - [ ] 实现时间加权QALY计算功能
  - [ ] 创建QALY折现计算（年度折现率）
  - [ ] 添加QALY计算的不确定性建模

- [ ] 任务2：实现生命年获得（LYG）计算 (AC: 2)
  - [ ] 创建src/modules/economics/lyg_calculator.py文件
  - [ ] 实现LYGCalculator类，计算挽救生命年
  - [ ] 添加生存曲线比较和差值计算
  - [ ] 实现年龄特异性生命年价值计算
  - [ ] 创建LYG的时间折现功能
  - [ ] 添加LYG统计显著性检验

- [ ] 任务3：实现健康效用值调整系统 (AC: 3)
  - [ ] 创建src/modules/economics/utility_values.py文件
  - [ ] 实现UtilityValueManager类，管理效用值
  - [ ] 添加年龄特异性效用值调整
  - [ ] 实现疾病状态特异性效用值
  - [ ] 创建治疗相关效用值变化建模
  - [ ] 添加效用值的时间衰减建模

- [ ] 任务4：创建生命质量权重配置系统 (AC: 4)
  - [ ] 创建data/utility_weights/目录结构
  - [ ] 设计效用值配置文件格式（YAML）
  - [ ] 实现效用值数据加载和管理
  - [ ] 添加多种效用值量表支持（EQ-5D、SF-6D等）
  - [ ] 创建效用值插值和外推功能
  - [ ] 实现效用值数据验证和质量控制

- [ ] 任务5：实现健康结果置信区间计算 (AC: 5)
  - [ ] 创建src/modules/economics/uncertainty_analysis.py文件
  - [ ] 实现UncertaintyAnalyzer类，计算置信区间
  - [ ] 添加Bootstrap方法计算QALY置信区间
  - [ ] 实现蒙特卡洛模拟的不确定性分析
  - [ ] 创建健康结果的概率敏感性分析
  - [ ] 添加置信区间可视化功能

- [ ] 任务6：创建健康结果验证测试套件 (AC: 6)
  - [ ] 创建tests/unit/test_qaly_calculator.py测试文件
  - [ ] 创建tests/unit/test_lyg_calculator.py测试文件
  - [ ] 实现QALY计算准确性验证测试
  - [ ] 添加LYG计算逻辑测试
  - [ ] 创建效用值调整功能测试
  - [ ] 实现置信区间计算验证测试

## Dev Notes

### 健康结果指标数据结构
```python
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
from enum import Enum
import numpy as np

class UtilityScale(Enum):
    EQ5D = "eq5d"           # EuroQol-5D
    SF6D = "sf6d"           # SF-6D
    HUI3 = "hui3"           # Health Utilities Index Mark 3
    CUSTOM = "custom"       # 自定义量表

@dataclass
class HealthState:
    state_name: str
    utility_value: float
    duration_years: float
    age_at_start: int
    confidence_interval: Optional[Tuple[float, float]] = None

@dataclass
class QALYResult:
    total_qalys: float
    undiscounted_qalys: float
    discounted_qalys: float
    qaly_by_age_group: Dict[str, float]
    confidence_interval: Optional[Tuple[float, float]] = None
    calculation_details: Dict = None
```

### QALY计算引擎实现
```python
class QALYCalculator:
    def __init__(self, discount_rate: float = 0.03):
        self.discount_rate = discount_rate
        self.utility_values = self._load_utility_values()
        self.age_adjustments = self._load_age_adjustments()
    
    def calculate_individual_qalys(
        self, 
        individual: Individual, 
        health_states: List[HealthState]
    ) -> QALYResult:
        """计算个体QALY"""
        
        total_qalys = 0.0
        undiscounted_qalys = 0.0
        qaly_by_age = {}
        
        current_age = individual.age
        
        for health_state in health_states:
            # 获取年龄调整的效用值
            adjusted_utility = self._get_age_adjusted_utility(
                health_state.utility_value, 
                current_age
            )
            
            # 计算该健康状态的QALY
            state_qalys = adjusted_utility * health_state.duration_years
            undiscounted_qalys += state_qalys
            
            # 计算折现QALY
            years_from_baseline = current_age - individual.baseline_age
            discount_factor = (1 + self.discount_rate) ** (-years_from_baseline)
            discounted_state_qalys = state_qalys * discount_factor
            total_qalys += discounted_state_qalys
            
            # 按年龄组记录
            age_group = self._get_age_group(current_age)
            qaly_by_age[age_group] = qaly_by_age.get(age_group, 0) + discounted_state_qalys
            
            current_age += health_state.duration_years
        
        return QALYResult(
            total_qalys=total_qalys,
            undiscounted_qalys=undiscounted_qalys,
            discounted_qalys=total_qalys,
            qaly_by_age_group=qaly_by_age
        )
    
    def calculate_population_qalys(
        self, 
        population: Population, 
        scenario_name: str
    ) -> Dict:
        """计算人群QALY"""
        
        individual_results = []
        for individual in population.individuals:
            health_states = self._extract_health_states(individual)
            qaly_result = self.calculate_individual_qalys(individual, health_states)
            individual_results.append(qaly_result)
        
        # 汇总统计
        total_qalys = sum(result.total_qalys for result in individual_results)
        mean_qalys = total_qalys / len(individual_results)
        
        return {
            'scenario_name': scenario_name,
            'total_population_qalys': total_qalys,
            'mean_individual_qalys': mean_qalys,
            'individual_results': individual_results,
            'qaly_distribution': self._calculate_qaly_distribution(individual_results)
        }
```

### 效用值配置文件格式
```yaml
# data/utility_weights/eq5d_china_2023.yaml
utility_weights:
  scale: "EQ5D"
  country: "China"
  version: "2023.1"
  source: "Chinese EQ-5D Value Set Study"
  
  baseline_utilities:
    healthy_population:
      age_18_29: 0.95
      age_30_39: 0.93
      age_40_49: 0.91
      age_50_59: 0.89
      age_60_69: 0.86
      age_70_79: 0.82
      age_80_plus: 0.78
  
  disease_state_utilities:
    normal: 1.0                           # 正常状态（相对于年龄基线）
    low_risk_adenoma: 0.98               # 低风险腺瘤（轻微焦虑）
    high_risk_adenoma: 0.96              # 高风险腺瘤（中度焦虑）
    preclinical_cancer: 1.0              # 临床前癌症（未知状态）
    clinical_cancer_stage_i: 0.85        # I期癌症
    clinical_cancer_stage_ii: 0.78       # II期癌症
    clinical_cancer_stage_iii: 0.70      # III期癌症
    clinical_cancer_stage_iv: 0.55       # IV期癌症
  
  treatment_related_utilities:
    screening_disutility:
      fit_test: -0.001                   # FIT检测轻微不适
      colonoscopy: -0.01                 # 结肠镜检查不适
      sigmoidoscopy: -0.005              # 乙状结肠镜不适
    
    treatment_disutility:
      surgery_acute: -0.15               # 手术急性期（3个月）
      chemotherapy: -0.20                # 化疗期间
      radiation_therapy: -0.10           # 放疗期间
      palliative_care: -0.25             # 姑息治疗
  
  uncertainty_parameters:
    standard_errors:
      healthy_baseline: 0.02
      cancer_stage_i: 0.05
      cancer_stage_ii: 0.06
      cancer_stage_iii: 0.08
      cancer_stage_iv: 0.10
```

### LYG计算实现
```python
class LYGCalculator:
    def __init__(self, life_table: LifeTable):
        self.life_table = life_table
    
    def calculate_lyg(
        self, 
        intervention_survival: List[float], 
        control_survival: List[float],
        ages: List[int]
    ) -> Dict:
        """计算生命年获得"""
        
        lyg_by_age = []
        total_lyg = 0.0
        
        for i, age in enumerate(ages):
            # 计算该年龄的生命年差异
            intervention_ly = intervention_survival[i]
            control_ly = control_survival[i]
            age_specific_lyg = intervention_ly - control_ly
            
            lyg_by_age.append({
                'age': age,
                'intervention_ly': intervention_ly,
                'control_ly': control_ly,
                'lyg': age_specific_lyg
            })
            
            total_lyg += age_specific_lyg
        
        return {
            'total_lyg': total_lyg,
            'lyg_by_age': lyg_by_age,
            'mean_lyg_per_person': total_lyg / len(ages) if ages else 0
        }
    
    def calculate_discounted_lyg(
        self, 
        lyg_result: Dict, 
        discount_rate: float = 0.03
    ) -> Dict:
        """计算折现生命年获得"""
        
        discounted_lyg = 0.0
        baseline_age = min(item['age'] for item in lyg_result['lyg_by_age'])
        
        for item in lyg_result['lyg_by_age']:
            years_from_baseline = item['age'] - baseline_age
            discount_factor = (1 + discount_rate) ** (-years_from_baseline)
            discounted_age_lyg = item['lyg'] * discount_factor
            discounted_lyg += discounted_age_lyg
            item['discounted_lyg'] = discounted_age_lyg
        
        lyg_result['total_discounted_lyg'] = discounted_lyg
        return lyg_result
```

### 不确定性分析实现
```python
class UncertaintyAnalyzer:
    def __init__(self, n_simulations: int = 1000):
        self.n_simulations = n_simulations
    
    def bootstrap_qaly_ci(
        self, 
        qaly_results: List[QALYResult], 
        confidence_level: float = 0.95
    ) -> Tuple[float, float]:
        """Bootstrap方法计算QALY置信区间"""
        
        bootstrap_means = []
        n_samples = len(qaly_results)
        
        for _ in range(self.n_simulations):
            # 有放回抽样
            bootstrap_sample = np.random.choice(
                [r.total_qalys for r in qaly_results], 
                size=n_samples, 
                replace=True
            )
            bootstrap_means.append(np.mean(bootstrap_sample))
        
        # 计算置信区间
        alpha = 1 - confidence_level
        lower_percentile = (alpha / 2) * 100
        upper_percentile = (1 - alpha / 2) * 100
        
        ci_lower = np.percentile(bootstrap_means, lower_percentile)
        ci_upper = np.percentile(bootstrap_means, upper_percentile)
        
        return (ci_lower, ci_upper)
    
    def monte_carlo_sensitivity_analysis(
        self, 
        base_parameters: Dict, 
        parameter_distributions: Dict,
        outcome_calculator: callable
    ) -> Dict:
        """蒙特卡洛敏感性分析"""
        
        results = []
        parameter_samples = []
        
        for _ in range(self.n_simulations):
            # 从分布中抽样参数
            sampled_params = {}
            for param_name, distribution in parameter_distributions.items():
                sampled_params[param_name] = distribution.rvs()
            
            parameter_samples.append(sampled_params)
            
            # 计算结果
            outcome = outcome_calculator(sampled_params)
            results.append(outcome)
        
        # 分析结果
        results_array = np.array(results)
        
        return {
            'mean_outcome': np.mean(results_array),
            'std_outcome': np.std(results_array),
            'percentile_2_5': np.percentile(results_array, 2.5),
            'percentile_97_5': np.percentile(results_array, 97.5),
            'parameter_samples': parameter_samples,
            'outcome_samples': results
        }
```

### Testing
#### 测试文件位置
- `tests/unit/test_qaly_calculator.py`
- `tests/unit/test_lyg_calculator.py`
- `tests/unit/test_utility_values.py`
- `tests/integration/test_health_outcomes.py`

#### 测试标准
- QALY计算准确性测试
- LYG计算逻辑验证测试
- 效用值调整功能测试
- 置信区间计算准确性测试
- 不确定性分析功能测试

#### 测试框架和模式
- 使用已知案例验证计算准确性
- 参数化测试验证不同健康状态
- 统计检验验证置信区间计算
- Mock数据测试边界条件

#### 特定测试要求
- QALY计算精度: 误差 < 0.001 QALY
- LYG计算准确性: 与理论值偏差 < 1%
- 置信区间覆盖率: 95%置信区间实际覆盖率 ≥ 94%
- 计算性能: 1000个个体QALY计算 < 5秒

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-31 | 1.0 | 初始故事创建 | Scrum Master |

## Dev Agent Record
*此部分将由开发代理在实施过程中填写*

### Agent Model Used
*待填写*

### Debug Log References
*待填写*

### Completion Notes List
*待填写*

### File List
*待填写*

## QA Results
*此部分将由QA代理在完成故事实施的QA审查后填写*
