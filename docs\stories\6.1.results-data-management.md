# Story 6.1: 结果数据管理

## Status
Draft

## Story
**As a** 数据分析师，
**I want** 高效管理和查询模拟结果数据，
**so that** 支持复杂的分析和报告需求。

## Acceptance Criteria
1. 实现模拟结果的结构化存储系统
2. 创建结果数据的索引和查询优化
3. 实现结果数据的版本控制和历史追踪
4. 添加数据完整性检查和修复功能
5. 创建结果数据的备份和恢复机制
6. 实现大规模结果数据的压缩存储

## Tasks / Subtasks

- [ ] 任务1：实现结构化存储系统 (AC: 1)
  - [ ] 创建src/data/results_storage.py文件
  - [ ] 实现ResultsStorage类，管理结果数据存储
  - [ ] 设计结果数据的关系型数据库模式
  - [ ] 实现SQLite/PostgreSQL的双重支持
  - [ ] 创建结果数据的分表和分区策略
  - [ ] 添加结果数据的CRUD操作接口

- [ ] 任务2：创建索引和查询优化系统 (AC: 2)
  - [ ] 创建src/data/query_optimizer.py文件
  - [ ] 实现QueryOptimizer类，优化数据查询
  - [ ] 添加多维索引和复合索引支持
  - [ ] 实现查询缓存和结果缓存机制
  - [ ] 创建查询性能监控和分析
  - [ ] 添加自动索引建议和优化功能

- [ ] 任务3：实现版本控制和历史追踪 (AC: 3)
  - [ ] 创建src/data/version_control.py文件
  - [ ] 实现DataVersionControl类，管理数据版本
  - [ ] 添加结果数据的时间戳和版本标记
  - [ ] 实现数据变更的审计日志功能
  - [ ] 创建版本间差异比较和回滚功能
  - [ ] 添加版本控制的可视化界面

- [ ] 任务4：添加数据完整性检查和修复 (AC: 4)
  - [ ] 创建src/data/integrity_checker.py文件
  - [ ] 实现DataIntegrityChecker类，检查数据完整性
  - [ ] 添加数据一致性验证和约束检查
  - [ ] 实现数据损坏检测和自动修复
  - [ ] 创建数据质量评估和报告功能
  - [ ] 添加数据清理和标准化工具

- [ ] 任务5：创建备份和恢复机制 (AC: 5)
  - [ ] 创建src/data/backup_manager.py文件
  - [ ] 实现BackupManager类，管理数据备份
  - [ ] 添加增量备份和全量备份支持
  - [ ] 实现自动备份调度和策略配置
  - [ ] 创建备份验证和完整性检查
  - [ ] 添加灾难恢复和数据迁移功能

- [ ] 任务6：实现大规模数据压缩存储 (AC: 6)
  - [ ] 创建src/data/compression_engine.py文件
  - [ ] 实现CompressionEngine类，处理数据压缩
  - [ ] 添加多种压缩算法支持（LZ4、ZSTD、GZIP）
  - [ ] 实现智能压缩策略和自适应选择
  - [ ] 创建压缩数据的透明访问接口
  - [ ] 添加压缩效果监控和优化建议

## Dev Notes

### 结果数据存储架构
```python
from dataclasses import dataclass
from typing import Dict, List, Optional, Any, Union
import sqlite3
import pandas as pd
from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Text, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import pickle
import json
from datetime import datetime

Base = declarative_base()

class SimulationRun(Base):
    __tablename__ = 'simulation_runs'
    
    id = Column(Integer, primary_key=True)
    run_name = Column(String(255), nullable=False)
    strategy_name = Column(String(255), nullable=False)
    parameters = Column(JSON)  # 存储参数配置
    start_time = Column(DateTime, default=datetime.utcnow)
    end_time = Column(DateTime)
    status = Column(String(50), default='running')
    version = Column(String(50), default='1.0')
    metadata = Column(JSON)

class PopulationResults(Base):
    __tablename__ = 'population_results'
    
    id = Column(Integer, primary_key=True)
    run_id = Column(Integer, nullable=False)
    age_group = Column(String(20))
    gender = Column(String(10))
    total_population = Column(Integer)
    adenoma_cases = Column(Integer)
    cancer_cases = Column(Integer)
    deaths = Column(Integer)
    qalys = Column(Float)
    costs = Column(Float)
    timestamp = Column(DateTime, default=datetime.utcnow)

class ScreeningResults(Base):
    __tablename__ = 'screening_results'
    
    id = Column(Integer, primary_key=True)
    run_id = Column(Integer, nullable=False)
    screening_tool = Column(String(50))
    age_group = Column(String(20))
    screenings_performed = Column(Integer)
    positive_results = Column(Integer)
    cancers_detected = Column(Integer)
    false_positives = Column(Integer)
    total_cost = Column(Float)
    timestamp = Column(DateTime, default=datetime.utcnow)
```

### 结果存储管理器
```python
class ResultsStorage:
    def __init__(self, database_url: str = "sqlite:///simulation_results.db"):
        self.engine = create_engine(database_url)
        Base.metadata.create_all(self.engine)
        Session = sessionmaker(bind=self.engine)
        self.session = Session()
        self.compression_engine = CompressionEngine()
        
    def store_simulation_run(
        self, 
        run_name: str, 
        strategy_name: str,
        parameters: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None
    ) -> int:
        """存储模拟运行信息"""
        
        run = SimulationRun(
            run_name=run_name,
            strategy_name=strategy_name,
            parameters=parameters,
            metadata=metadata or {}
        )
        
        self.session.add(run)
        self.session.commit()
        
        return run.id
    
    def store_population_results(
        self, 
        run_id: int, 
        results: List[Dict[str, Any]]
    ):
        """批量存储人群结果"""
        
        population_results = []
        for result in results:
            pop_result = PopulationResults(
                run_id=run_id,
                age_group=result.get('age_group'),
                gender=result.get('gender'),
                total_population=result.get('total_population'),
                adenoma_cases=result.get('adenoma_cases'),
                cancer_cases=result.get('cancer_cases'),
                deaths=result.get('deaths'),
                qalys=result.get('qalys'),
                costs=result.get('costs')
            )
            population_results.append(pop_result)
        
        self.session.bulk_save_objects(population_results)
        self.session.commit()
    
    def store_large_dataset(
        self, 
        run_id: int, 
        dataset_name: str,
        data: Union[pd.DataFrame, Dict, List],
        compress: bool = True
    ):
        """存储大型数据集（使用压缩）"""
        
        if compress:
            compressed_data = self.compression_engine.compress_data(data)
            storage_path = f"data/compressed/{run_id}_{dataset_name}.zst"
        else:
            compressed_data = data
            storage_path = f"data/raw/{run_id}_{dataset_name}.pkl"
        
        # 存储到文件系统
        os.makedirs(os.path.dirname(storage_path), exist_ok=True)
        
        if isinstance(data, pd.DataFrame):
            if compress:
                data.to_parquet(storage_path, compression='snappy')
            else:
                data.to_pickle(storage_path)
        else:
            with open(storage_path, 'wb') as f:
                if compress:
                    pickle.dump(compressed_data, f)
                else:
                    pickle.dump(data, f)
        
        # 在数据库中记录文件路径
        metadata_record = {
            'run_id': run_id,
            'dataset_name': dataset_name,
            'file_path': storage_path,
            'compressed': compress,
            'size_bytes': os.path.getsize(storage_path),
            'created_at': datetime.utcnow().isoformat()
        }
        
        # 存储元数据到专门的表
        self._store_dataset_metadata(metadata_record)
```

### 查询优化器
```python
class QueryOptimizer:
    def __init__(self, storage: ResultsStorage):
        self.storage = storage
        self.query_cache = {}
        self.cache_ttl = 3600  # 1小时缓存
        
    def query_results(
        self, 
        query_spec: Dict[str, Any],
        use_cache: bool = True
    ) -> pd.DataFrame:
        """优化的结果查询"""
        
        # 生成查询缓存键
        cache_key = self._generate_cache_key(query_spec)
        
        # 检查缓存
        if use_cache and cache_key in self.query_cache:
            cached_result, timestamp = self.query_cache[cache_key]
            if (datetime.now() - timestamp).seconds < self.cache_ttl:
                return cached_result
        
        # 构建优化的SQL查询
        optimized_query = self._build_optimized_query(query_spec)
        
        # 执行查询
        result = pd.read_sql(optimized_query, self.storage.engine)
        
        # 缓存结果
        if use_cache:
            self.query_cache[cache_key] = (result, datetime.now())
        
        return result
    
    def _build_optimized_query(self, query_spec: Dict[str, Any]) -> str:
        """构建优化的SQL查询"""
        
        base_query = """
        SELECT pr.*, sr.screening_tool, sr.screenings_performed
        FROM population_results pr
        LEFT JOIN screening_results sr ON pr.run_id = sr.run_id 
            AND pr.age_group = sr.age_group
        WHERE 1=1
        """
        
        conditions = []
        
        # 添加筛选条件
        if 'run_ids' in query_spec:
            run_ids = ','.join(map(str, query_spec['run_ids']))
            conditions.append(f"pr.run_id IN ({run_ids})")
        
        if 'age_groups' in query_spec:
            age_groups = "','".join(query_spec['age_groups'])
            conditions.append(f"pr.age_group IN ('{age_groups}')")
        
        if 'genders' in query_spec:
            genders = "','".join(query_spec['genders'])
            conditions.append(f"pr.gender IN ('{genders}')")
        
        # 添加日期范围
        if 'date_range' in query_spec:
            start_date, end_date = query_spec['date_range']
            conditions.append(f"pr.timestamp BETWEEN '{start_date}' AND '{end_date}'")
        
        # 组合查询
        if conditions:
            base_query += " AND " + " AND ".join(conditions)
        
        # 添加排序和限制
        if 'order_by' in query_spec:
            base_query += f" ORDER BY {query_spec['order_by']}"
        
        if 'limit' in query_spec:
            base_query += f" LIMIT {query_spec['limit']}"
        
        return base_query
    
    def analyze_query_performance(self, query: str) -> Dict[str, Any]:
        """分析查询性能"""
        
        # 执行查询计划分析
        explain_query = f"EXPLAIN QUERY PLAN {query}"
        
        with self.storage.engine.connect() as conn:
            result = conn.execute(explain_query)
            query_plan = result.fetchall()
        
        # 分析性能瓶颈
        performance_analysis = {
            'query_plan': query_plan,
            'estimated_cost': self._estimate_query_cost(query_plan),
            'index_recommendations': self._suggest_indexes(query_plan),
            'optimization_suggestions': self._generate_optimization_suggestions(query_plan)
        }
        
        return performance_analysis
```

### 数据完整性检查器
```python
class DataIntegrityChecker:
    def __init__(self, storage: ResultsStorage):
        self.storage = storage
        self.integrity_rules = self._load_integrity_rules()
        
    def check_data_integrity(self, run_id: Optional[int] = None) -> Dict[str, Any]:
        """检查数据完整性"""
        
        integrity_report = {
            'overall_status': 'PASS',
            'issues_found': [],
            'statistics': {},
            'recommendations': []
        }
        
        # 检查基本数据完整性
        basic_checks = self._run_basic_integrity_checks(run_id)
        integrity_report['basic_checks'] = basic_checks
        
        # 检查业务逻辑完整性
        business_checks = self._run_business_logic_checks(run_id)
        integrity_report['business_checks'] = business_checks
        
        # 检查数据一致性
        consistency_checks = self._run_consistency_checks(run_id)
        integrity_report['consistency_checks'] = consistency_checks
        
        # 汇总结果
        all_issues = (basic_checks.get('issues', []) + 
                     business_checks.get('issues', []) + 
                     consistency_checks.get('issues', []))
        
        if all_issues:
            integrity_report['overall_status'] = 'FAIL'
            integrity_report['issues_found'] = all_issues
        
        # 生成修复建议
        integrity_report['recommendations'] = self._generate_repair_recommendations(all_issues)
        
        return integrity_report
    
    def _run_basic_integrity_checks(self, run_id: Optional[int]) -> Dict[str, Any]:
        """运行基本完整性检查"""
        
        checks = {
            'null_values': self._check_null_values(run_id),
            'data_types': self._check_data_types(run_id),
            'foreign_keys': self._check_foreign_key_constraints(run_id),
            'duplicates': self._check_duplicates(run_id)
        }
        
        issues = []
        for check_name, check_result in checks.items():
            if not check_result['passed']:
                issues.extend(check_result['issues'])
        
        return {
            'checks': checks,
            'issues': issues,
            'passed': len(issues) == 0
        }
    
    def _check_business_logic_constraints(self, run_id: Optional[int]) -> List[Dict]:
        """检查业务逻辑约束"""
        
        issues = []
        
        # 检查人群数量逻辑
        population_query = """
        SELECT run_id, age_group, gender, 
               total_population, adenoma_cases, cancer_cases, deaths
        FROM population_results
        WHERE (adenoma_cases > total_population 
               OR cancer_cases > total_population 
               OR deaths > total_population
               OR adenoma_cases + cancer_cases > total_population)
        """
        
        if run_id:
            population_query += f" AND run_id = {run_id}"
        
        invalid_populations = pd.read_sql(population_query, self.storage.engine)
        
        for _, row in invalid_populations.iterrows():
            issues.append({
                'type': 'business_logic_violation',
                'table': 'population_results',
                'run_id': row['run_id'],
                'description': f"人群数量逻辑错误: 年龄组{row['age_group']}, 性别{row['gender']}",
                'severity': 'high'
            })
        
        return issues
    
    def repair_data_issues(self, issues: List[Dict]) -> Dict[str, Any]:
        """修复数据问题"""
        
        repair_report = {
            'repaired_issues': [],
            'failed_repairs': [],
            'manual_intervention_required': []
        }
        
        for issue in issues:
            try:
                if issue['type'] == 'null_values':
                    self._repair_null_values(issue)
                    repair_report['repaired_issues'].append(issue)
                elif issue['type'] == 'duplicates':
                    self._repair_duplicates(issue)
                    repair_report['repaired_issues'].append(issue)
                elif issue['type'] == 'business_logic_violation':
                    # 业务逻辑问题需要手动干预
                    repair_report['manual_intervention_required'].append(issue)
                else:
                    repair_report['failed_repairs'].append(issue)
            except Exception as e:
                issue['repair_error'] = str(e)
                repair_report['failed_repairs'].append(issue)
        
        return repair_report
```

### 备份管理器
```python
class BackupManager:
    def __init__(self, storage: ResultsStorage):
        self.storage = storage
        self.backup_config = self._load_backup_config()
        
    def create_backup(
        self, 
        backup_type: str = "incremental",
        backup_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """创建数据备份"""
        
        if backup_name is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"backup_{backup_type}_{timestamp}"
        
        backup_path = f"backups/{backup_name}"
        os.makedirs(backup_path, exist_ok=True)
        
        backup_info = {
            'backup_name': backup_name,
            'backup_type': backup_type,
            'backup_path': backup_path,
            'start_time': datetime.now(),
            'status': 'in_progress'
        }
        
        try:
            if backup_type == "full":
                self._create_full_backup(backup_path)
            elif backup_type == "incremental":
                self._create_incremental_backup(backup_path)
            else:
                raise ValueError(f"Unknown backup type: {backup_type}")
            
            backup_info['status'] = 'completed'
            backup_info['end_time'] = datetime.now()
            
            # 验证备份完整性
            verification_result = self._verify_backup(backup_path)
            backup_info['verification'] = verification_result
            
        except Exception as e:
            backup_info['status'] = 'failed'
            backup_info['error'] = str(e)
        
        # 记录备份信息
        self._record_backup_info(backup_info)
        
        return backup_info
    
    def restore_from_backup(
        self, 
        backup_name: str,
        target_database: Optional[str] = None
    ) -> Dict[str, Any]:
        """从备份恢复数据"""
        
        backup_path = f"backups/{backup_name}"
        
        if not os.path.exists(backup_path):
            raise FileNotFoundError(f"Backup not found: {backup_name}")
        
        restore_info = {
            'backup_name': backup_name,
            'start_time': datetime.now(),
            'status': 'in_progress'
        }
        
        try:
            # 创建恢复点
            self._create_restore_point()
            
            # 执行恢复
            if target_database:
                self._restore_to_new_database(backup_path, target_database)
            else:
                self._restore_to_current_database(backup_path)
            
            restore_info['status'] = 'completed'
            restore_info['end_time'] = datetime.now()
            
        except Exception as e:
            restore_info['status'] = 'failed'
            restore_info['error'] = str(e)
            
            # 尝试回滚到恢复点
            self._rollback_to_restore_point()
        
        return restore_info
```

### Testing
#### 测试文件位置
- `tests/unit/test_results_storage.py`
- `tests/unit/test_query_optimizer.py`
- `tests/unit/test_data_integrity.py`
- `tests/integration/test_results_management.py`

#### 测试标准
- 数据存储和检索准确性测试
- 查询优化性能测试
- 数据完整性检查测试
- 备份和恢复功能测试
- 大规模数据处理测试

#### 测试框架和模式
- 使用内存数据库测试存储功能
- 性能基准测试验证查询优化
- 模拟数据损坏测试完整性检查
- 集成测试验证完整数据管理流程

#### 特定测试要求
- 数据存储准确性: 100%数据完整性
- 查询性能: 复杂查询 < 5秒
- 备份效率: 1GB数据备份 < 2分钟
- 恢复准确性: 100%数据恢复成功率

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-31 | 1.0 | 初始故事创建 | Scrum Master |

## Dev Agent Record
*此部分将由开发代理在实施过程中填写*

### Agent Model Used
*待填写*

### Debug Log References
*待填写*

### Completion Notes List
*待填写*

### File List
*待填写*

## QA Results
*此部分将由QA代理在完成故事实施的QA审查后填写*
