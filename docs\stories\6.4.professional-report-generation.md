# Story 6.4: 专业报告生成

## Status
Draft

## Story
**As a** 研究人员，
**I want** 生成专业的分析报告，
**so that** 向利益相关者展示研究结果。

## Acceptance Criteria
1. 创建标准化的报告模板系统
2. 实现报告内容的自动填充和格式化
3. 添加图表、表格的自动嵌入功能
4. 创建报告的多格式导出（PDF、Word、HTML）
5. 实现报告的版本控制和协作编辑
6. 添加报告质量检查和验证功能

## Tasks / Subtasks

- [ ] 任务1：创建标准化报告模板系统 (AC: 1)
  - [ ] 创建src/reporting/template_manager.py文件
  - [ ] 实现ReportTemplateManager类，管理报告模板
  - [ ] 设计多种报告模板（研究报告、政策简报、技术文档）
  - [ ] 添加模板的自定义和扩展功能
  - [ ] 创建模板版本管理和更新机制
  - [ ] 实现模板预览和选择界面

- [ ] 任务2：实现报告内容自动填充 (AC: 2)
  - [ ] 创建src/reporting/content_generator.py文件
  - [ ] 实现ContentGenerator类，自动生成报告内容
  - [ ] 添加数据驱动的内容生成功能
  - [ ] 实现智能文本生成和摘要提取
  - [ ] 创建内容格式化和样式应用
  - [ ] 添加多语言内容生成支持

- [ ] 任务3：添加图表表格自动嵌入 (AC: 3)
  - [ ] 创建src/reporting/media_embedder.py文件
  - [ ] 实现MediaEmbedder类，处理媒体嵌入
  - [ ] 添加图表的自动生成和嵌入
  - [ ] 实现表格的格式化和嵌入
  - [ ] 创建图表标题和说明的自动生成
  - [ ] 添加媒体质量控制和优化

- [ ] 任务4：创建多格式导出功能 (AC: 4)
  - [ ] 创建src/reporting/export_engine.py文件
  - [ ] 实现ReportExportEngine类，处理格式导出
  - [ ] 添加PDF导出功能（使用ReportLab）
  - [ ] 实现Word文档导出（使用python-docx）
  - [ ] 创建HTML报告导出和Web展示
  - [ ] 添加导出格式的自定义配置

- [ ] 任务5：实现版本控制和协作编辑 (AC: 5)
  - [ ] 创建src/reporting/version_control.py文件
  - [ ] 实现ReportVersionControl类，管理报告版本
  - [ ] 添加报告变更跟踪和历史记录
  - [ ] 实现多用户协作编辑功能
  - [ ] 创建版本比较和合并功能
  - [ ] 添加评论和审阅工作流

- [ ] 任务6：添加报告质量检查验证 (AC: 6)
  - [ ] 创建src/reporting/quality_checker.py文件
  - [ ] 实现ReportQualityChecker类，检查报告质量
  - [ ] 添加内容完整性和一致性检查
  - [ ] 实现格式规范和样式验证
  - [ ] 创建数据准确性和引用检查
  - [ ] 添加报告改进建议生成

## Dev Notes

### 报告模板系统架构
```python
from dataclasses import dataclass
from typing import Dict, List, Optional, Any, Union
from jinja2 import Template, Environment, FileSystemLoader
import yaml
import json
from datetime import datetime
from enum import Enum

class ReportType(Enum):
    RESEARCH_REPORT = "research_report"
    POLICY_BRIEF = "policy_brief"
    TECHNICAL_DOCUMENT = "technical_document"
    EXECUTIVE_SUMMARY = "executive_summary"
    COMPARISON_ANALYSIS = "comparison_analysis"

@dataclass
class ReportTemplate:
    template_id: str
    template_name: str
    report_type: ReportType
    template_file: str
    style_file: str
    sections: List[str]
    required_data: List[str]
    optional_data: List[str]
    metadata: Dict[str, Any]

@dataclass
class ReportContent:
    title: str
    authors: List[str]
    date: datetime
    abstract: str
    sections: Dict[str, Any]
    figures: List[Dict[str, Any]]
    tables: List[Dict[str, Any]]
    references: List[str]
    appendices: Dict[str, Any]
```

### 报告模板管理器
```python
class ReportTemplateManager:
    def __init__(self, templates_dir: str = "templates/reports"):
        self.templates_dir = templates_dir
        self.jinja_env = Environment(loader=FileSystemLoader(templates_dir))
        self.templates = self._load_templates()
        
    def _load_templates(self) -> Dict[str, ReportTemplate]:
        """加载所有报告模板"""
        templates = {}
        
        # 扫描模板目录
        template_configs = glob.glob(f"{self.templates_dir}/**/template.yaml", recursive=True)
        
        for config_file in template_configs:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            template = ReportTemplate(
                template_id=config['template_id'],
                template_name=config['template_name'],
                report_type=ReportType(config['report_type']),
                template_file=config['template_file'],
                style_file=config.get('style_file', ''),
                sections=config['sections'],
                required_data=config['required_data'],
                optional_data=config.get('optional_data', []),
                metadata=config.get('metadata', {})
            )
            
            templates[template.template_id] = template
        
        return templates
    
    def get_template(self, template_id: str) -> Optional[ReportTemplate]:
        """获取指定模板"""
        return self.templates.get(template_id)
    
    def list_templates_by_type(self, report_type: ReportType) -> List[ReportTemplate]:
        """按类型列出模板"""
        return [t for t in self.templates.values() if t.report_type == report_type]
    
    def render_template(
        self, 
        template_id: str, 
        content_data: Dict[str, Any]
    ) -> str:
        """渲染模板"""
        template = self.get_template(template_id)
        if not template:
            raise ValueError(f"Template not found: {template_id}")
        
        # 验证必需数据
        missing_data = []
        for required_field in template.required_data:
            if required_field not in content_data:
                missing_data.append(required_field)
        
        if missing_data:
            raise ValueError(f"Missing required data: {missing_data}")
        
        # 加载Jinja2模板
        jinja_template = self.jinja_env.get_template(template.template_file)
        
        # 渲染模板
        rendered_content = jinja_template.render(**content_data)
        
        return rendered_content
```

### 内容生成器
```python
class ContentGenerator:
    def __init__(self, data_manager):
        self.data_manager = data_manager
        self.text_generators = {
            'executive_summary': self._generate_executive_summary,
            'methodology': self._generate_methodology_section,
            'results': self._generate_results_section,
            'discussion': self._generate_discussion_section,
            'conclusions': self._generate_conclusions_section
        }
        
    def generate_report_content(
        self, 
        simulation_results: Dict[str, Any],
        template: ReportTemplate
    ) -> ReportContent:
        """生成完整报告内容"""
        
        # 基本信息
        title = self._generate_title(simulation_results, template.report_type)
        authors = simulation_results.get('authors', ['系统生成'])
        date = datetime.now()
        
        # 生成各个章节
        sections = {}
        for section_name in template.sections:
            if section_name in self.text_generators:
                sections[section_name] = self.text_generators[section_name](
                    simulation_results
                )
            else:
                sections[section_name] = self._generate_generic_section(
                    section_name, simulation_results
                )
        
        # 生成摘要
        abstract = self._generate_abstract(simulation_results, sections)
        
        # 生成图表和表格
        figures = self._generate_figures(simulation_results)
        tables = self._generate_tables(simulation_results)
        
        # 生成参考文献
        references = self._generate_references(simulation_results)
        
        return ReportContent(
            title=title,
            authors=authors,
            date=date,
            abstract=abstract,
            sections=sections,
            figures=figures,
            tables=tables,
            references=references,
            appendices={}
        )
    
    def _generate_executive_summary(self, results: Dict[str, Any]) -> str:
        """生成执行摘要"""
        
        # 提取关键结果
        key_findings = []
        
        if 'cost_effectiveness' in results:
            ce_results = results['cost_effectiveness']
            best_strategy = min(ce_results.items(), key=lambda x: x[1].get('icer', float('inf')))
            key_findings.append(
                f"最具成本效益的策略是{best_strategy[0]}，"
                f"ICER为{best_strategy[1].get('icer', 0):,.0f}元/QALY。"
            )
        
        if 'health_outcomes' in results:
            health_results = results['health_outcomes']
            total_qalys = sum(strategy.get('total_qalys', 0) 
                            for strategy in health_results.values())
            key_findings.append(
                f"所有策略总计可获得{total_qalys:,.0f}个质量调整生命年。"
            )
        
        if 'screening_performance' in results:
            screening_results = results['screening_performance']
            total_cancers_detected = sum(strategy.get('cancers_detected', 0) 
                                       for strategy in screening_results.values())
            key_findings.append(
                f"筛查策略总计可检出{total_cancers_detected:,.0f}例癌症。"
            )
        
        # 组合成执行摘要
        summary = f"""
        本研究评估了{len(results.get('strategies', []))}种结直肠癌筛查策略的成本效益。
        
        主要发现：
        {chr(10).join(f"• {finding}" for finding in key_findings)}
        
        基于分析结果，建议实施最具成本效益的筛查策略以优化资源配置。
        """
        
        return summary.strip()
    
    def _generate_methodology_section(self, results: Dict[str, Any]) -> str:
        """生成方法学章节"""
        
        methodology = f"""
        ## 研究方法
        
        ### 模型设计
        本研究采用微观模拟模型评估结直肠癌筛查策略的成本效益。模型模拟了
        {results.get('population_size', 100000):,}名个体从{results.get('start_age', 50)}岁到
        {results.get('end_age', 85)}岁的生命历程。
        
        ### 疾病自然史
        模型包含两种主要的癌症发展路径：
        1. 腺瘤-癌变通路（85%的病例）
        2. 锯齿状腺瘤通路（15%的病例）
        
        ### 筛查策略
        评估的筛查策略包括：
        {chr(10).join(f"• {strategy}" for strategy in results.get('strategies', []))}
        
        ### 经济学参数
        成本和效用值基于中国的卫生经济学数据，采用3%的年度折现率。
        支付意愿阈值设定为{results.get('wtp_threshold', 150000):,}元/QALY。
        
        ### 不确定性分析
        采用概率敏感性分析评估参数不确定性对结果的影响，
        进行了{results.get('n_simulations', 1000):,}次蒙特卡洛模拟。
        """
        
        return methodology.strip()
```

### 媒体嵌入器
```python
class MediaEmbedder:
    def __init__(self):
        self.figure_counter = 0
        self.table_counter = 0
        
    def embed_chart(
        self, 
        chart_data: Dict[str, Any], 
        title: str,
        caption: str = ""
    ) -> Dict[str, Any]:
        """嵌入图表"""
        
        self.figure_counter += 1
        figure_id = f"fig_{self.figure_counter}"
        
        # 生成图表
        if chart_data['type'] == 'bar':
            fig = self._create_bar_chart(chart_data)
        elif chart_data['type'] == 'line':
            fig = self._create_line_chart(chart_data)
        elif chart_data['type'] == 'scatter':
            fig = self._create_scatter_chart(chart_data)
        else:
            raise ValueError(f"Unsupported chart type: {chart_data['type']}")
        
        # 保存图表
        chart_path = f"temp/figures/{figure_id}.png"
        fig.write_image(chart_path, width=800, height=600, scale=2)
        
        return {
            'id': figure_id,
            'title': title,
            'caption': caption,
            'path': chart_path,
            'number': self.figure_counter,
            'type': 'figure'
        }
    
    def embed_table(
        self, 
        table_data: pd.DataFrame, 
        title: str,
        caption: str = ""
    ) -> Dict[str, Any]:
        """嵌入表格"""
        
        self.table_counter += 1
        table_id = f"table_{self.table_counter}"
        
        # 格式化表格
        formatted_table = self._format_table(table_data)
        
        return {
            'id': table_id,
            'title': title,
            'caption': caption,
            'data': formatted_table,
            'number': self.table_counter,
            'type': 'table'
        }
    
    def _format_table(self, df: pd.DataFrame) -> Dict[str, Any]:
        """格式化表格数据"""
        
        # 数值格式化
        formatted_df = df.copy()
        
        for col in formatted_df.columns:
            if formatted_df[col].dtype in ['float64', 'int64']:
                if 'cost' in col.lower() or '成本' in col:
                    # 成本格式化
                    formatted_df[col] = formatted_df[col].apply(
                        lambda x: f"{x:,.0f}" if pd.notna(x) else ""
                    )
                elif 'qaly' in col.lower():
                    # QALY格式化
                    formatted_df[col] = formatted_df[col].apply(
                        lambda x: f"{x:.3f}" if pd.notna(x) else ""
                    )
                elif 'rate' in col.lower() or '率' in col:
                    # 比率格式化
                    formatted_df[col] = formatted_df[col].apply(
                        lambda x: f"{x:.1%}" if pd.notna(x) else ""
                    )
                else:
                    # 默认数值格式化
                    formatted_df[col] = formatted_df[col].apply(
                        lambda x: f"{x:.2f}" if pd.notna(x) else ""
                    )
        
        return {
            'headers': formatted_df.columns.tolist(),
            'rows': formatted_df.values.tolist(),
            'index': formatted_df.index.tolist()
        }
```

### 导出引擎
```python
class ReportExportEngine:
    def __init__(self):
        self.export_handlers = {
            'pdf': self._export_to_pdf,
            'docx': self._export_to_docx,
            'html': self._export_to_html
        }
    
    def export_report(
        self, 
        report_content: ReportContent,
        template: ReportTemplate,
        output_path: str,
        format: str = 'pdf'
    ):
        """导出报告"""
        
        if format not in self.export_handlers:
            raise ValueError(f"Unsupported export format: {format}")
        
        handler = self.export_handlers[format]
        handler(report_content, template, output_path)
    
    def _export_to_pdf(
        self, 
        content: ReportContent, 
        template: ReportTemplate,
        output_path: str
    ):
        """导出为PDF"""
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, Table
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        
        doc = SimpleDocTemplate(output_path, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        # 标题
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1  # 居中
        )
        story.append(Paragraph(content.title, title_style))
        story.append(Spacer(1, 12))
        
        # 作者和日期
        author_text = f"作者: {', '.join(content.authors)}"
        date_text = f"日期: {content.date.strftime('%Y年%m月%d日')}"
        story.append(Paragraph(author_text, styles['Normal']))
        story.append(Paragraph(date_text, styles['Normal']))
        story.append(Spacer(1, 20))
        
        # 摘要
        if content.abstract:
            story.append(Paragraph("摘要", styles['Heading2']))
            story.append(Paragraph(content.abstract, styles['Normal']))
            story.append(Spacer(1, 20))
        
        # 各个章节
        for section_name, section_content in content.sections.items():
            story.append(Paragraph(section_name.replace('_', ' ').title(), styles['Heading2']))
            
            # 处理段落
            paragraphs = section_content.split('\n\n')
            for para in paragraphs:
                if para.strip():
                    story.append(Paragraph(para.strip(), styles['Normal']))
                    story.append(Spacer(1, 12))
        
        # 添加图表
        for figure in content.figures:
            if os.path.exists(figure['path']):
                story.append(Paragraph(f"图 {figure['number']}: {figure['title']}", styles['Heading3']))
                story.append(Image(figure['path'], width=6*inch, height=4*inch))
                if figure['caption']:
                    story.append(Paragraph(figure['caption'], styles['Normal']))
                story.append(Spacer(1, 20))
        
        # 添加表格
        for table in content.tables:
            story.append(Paragraph(f"表 {table['number']}: {table['title']}", styles['Heading3']))
            
            # 创建表格数据
            table_data = [table['data']['headers']] + table['data']['rows']
            pdf_table = Table(table_data)
            pdf_table.setStyle([
                ('BACKGROUND', (0, 0), (-1, 0), '#f0f0f0'),
                ('TEXTCOLOR', (0, 0), (-1, 0), '#000000'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), '#ffffff'),
                ('GRID', (0, 0), (-1, -1), 1, '#000000')
            ])
            
            story.append(pdf_table)
            if table['caption']:
                story.append(Paragraph(table['caption'], styles['Normal']))
            story.append(Spacer(1, 20))
        
        # 构建PDF
        doc.build(story)
    
    def _export_to_html(
        self, 
        content: ReportContent, 
        template: ReportTemplate,
        output_path: str
    ):
        """导出为HTML"""
        
        html_template = """
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{{ title }}</title>
            <style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 40px; }
                h1 { color: #2c3e50; text-align: center; }
                h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 5px; }
                h3 { color: #7f8c8d; }
                .abstract { background-color: #f8f9fa; padding: 20px; border-left: 4px solid #3498db; }
                .figure { text-align: center; margin: 20px 0; }
                .table { margin: 20px 0; }
                table { border-collapse: collapse; width: 100%; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                th { background-color: #f2f2f2; }
            </style>
        </head>
        <body>
            <h1>{{ title }}</h1>
            <p><strong>作者:</strong> {{ authors|join(', ') }}</p>
            <p><strong>日期:</strong> {{ date.strftime('%Y年%m月%d日') }}</p>
            
            {% if abstract %}
            <div class="abstract">
                <h2>摘要</h2>
                <p>{{ abstract }}</p>
            </div>
            {% endif %}
            
            {% for section_name, section_content in sections.items() %}
            <h2>{{ section_name.replace('_', ' ').title() }}</h2>
            {% for paragraph in section_content.split('\n\n') %}
                {% if paragraph.strip() %}
                <p>{{ paragraph.strip() }}</p>
                {% endif %}
            {% endfor %}
            {% endfor %}
            
            {% for figure in figures %}
            <div class="figure">
                <h3>图 {{ figure.number }}: {{ figure.title }}</h3>
                <img src="{{ figure.path }}" alt="{{ figure.title }}" style="max-width: 100%;">
                {% if figure.caption %}
                <p>{{ figure.caption }}</p>
                {% endif %}
            </div>
            {% endfor %}
            
            {% for table in tables %}
            <div class="table">
                <h3>表 {{ table.number }}: {{ table.title }}</h3>
                <table>
                    <thead>
                        <tr>
                            {% for header in table.data.headers %}
                            <th>{{ header }}</th>
                            {% endfor %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for row in table.data.rows %}
                        <tr>
                            {% for cell in row %}
                            <td>{{ cell }}</td>
                            {% endfor %}
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                {% if table.caption %}
                <p>{{ table.caption }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </body>
        </html>
        """
        
        from jinja2 import Template
        template_obj = Template(html_template)
        
        html_content = template_obj.render(
            title=content.title,
            authors=content.authors,
            date=content.date,
            abstract=content.abstract,
            sections=content.sections,
            figures=content.figures,
            tables=content.tables
        )
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
```

### Testing
#### 测试文件位置
- `tests/unit/test_report_templates.py`
- `tests/unit/test_content_generator.py`
- `tests/unit/test_export_engine.py`
- `tests/integration/test_report_generation.py`

#### 测试标准
- 模板系统功能测试
- 内容生成准确性测试
- 多格式导出功能测试
- 版本控制功能测试
- 报告质量检查测试

#### 测试框架和模式
- 使用模拟数据测试报告生成
- 文件比较测试验证导出格式
- 模板渲染测试验证内容正确性
- 集成测试验证完整报告流程

#### 特定测试要求
- 报告生成准确性: 100%内容正确性
- 导出格式完整性: 所有格式正常显示
- 模板渲染性能: 复杂报告 < 30秒
- 质量检查覆盖率: 100%规则检查

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-31 | 1.0 | 初始故事创建 | Scrum Master |

## Dev Agent Record
*此部分将由开发代理在实施过程中填写*

### Agent Model Used
*待填写*

### Debug Log References
*待填写*

### Completion Notes List
*待填写*

### File List
*待填写*

## QA Results
*此部分将由QA代理在完成故事实施的QA审查后填写*
