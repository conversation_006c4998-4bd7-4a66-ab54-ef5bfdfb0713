# Story 6.2: 交互式可视化分析面板

## Status
Draft

## Story
**As a** 决策者，
**I want** 通过交互式分析面板探索模拟结果，
**so that** 深入理解不同策略的影响。

## Acceptance Criteria
1. 创建响应式的桌面分析面板界面
2. 实现动态图表和交互式可视化组件
3. 添加筛选、排序和钻取功能
4. 创建自定义视图和个人化设置
5. 实现实时数据更新和刷新机制
6. 添加分析面板的导出和保存功能

## Tasks / Subtasks

- [ ] 任务1：创建响应式分析面板界面 (AC: 1)
  - [ ] 创建src/interfaces/desktop/windows/analysis_panel.py文件
  - [ ] 实现AnalysisPanel类，主要分析界面
  - [ ] 设计响应式布局和自适应界面
  - [ ] 添加多标签页和可停靠窗口支持
  - [ ] 创建工具栏和状态栏功能
  - [ ] 实现界面主题和样式配置

- [ ] 任务2：实现动态图表和可视化组件 (AC: 2)
  - [ ] 创建src/interfaces/desktop/widgets/chart_widgets.py文件
  - [ ] 实现ChartWidget基类和各种图表类型
  - [ ] 添加交互式图表（缩放、平移、选择）
  - [ ] 创建动态数据绑定和实时更新
  - [ ] 实现图表动画和过渡效果
  - [ ] 添加图表导出和保存功能

- [ ] 任务3：添加筛选排序和钻取功能 (AC: 3)
  - [ ] 创建src/interfaces/desktop/widgets/filter_panel.py文件
  - [ ] 实现FilterPanel类，数据筛选界面
  - [ ] 添加多维度筛选和条件组合
  - [ ] 实现数据排序和分组功能
  - [ ] 创建钻取导航和层级展示
  - [ ] 添加筛选历史和快速筛选

- [ ] 任务4：创建自定义视图和个人化设置 (AC: 4)
  - [ ] 创建src/interfaces/desktop/widgets/view_manager.py文件
  - [ ] 实现ViewManager类，管理自定义视图
  - [ ] 添加视图模板和预设配置
  - [ ] 实现用户偏好设置和保存
  - [ ] 创建视图分享和导入功能
  - [ ] 添加个人化仪表板配置

- [ ] 任务5：实现实时数据更新机制 (AC: 5)
  - [ ] 创建src/services/real_time_updater.py文件
  - [ ] 实现RealTimeUpdater类，管理数据更新
  - [ ] 添加数据变更监听和通知机制
  - [ ] 实现增量数据更新和刷新
  - [ ] 创建更新频率控制和优化
  - [ ] 添加更新状态指示和进度显示

- [ ] 任务6：添加导出和保存功能 (AC: 6)
  - [ ] 创建src/services/panel_export_service.py文件
  - [ ] 实现PanelExportService类，处理导出功能
  - [ ] 添加面板截图和图像导出
  - [ ] 实现分析报告的自动生成
  - [ ] 创建交互式HTML报告导出
  - [ ] 添加批量导出和定时导出功能

## Dev Notes

### 分析面板架构设计
```python
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

@dataclass
class ViewConfig:
    view_id: str
    view_name: str
    layout_config: Dict[str, Any]
    chart_configs: List[Dict[str, Any]]
    filter_configs: Dict[str, Any]
    user_preferences: Dict[str, Any]

class AnalysisPanel(QMainWindow):
    def __init__(self):
        super().__init__()
        self.view_manager = ViewManager()
        self.chart_widgets = {}
        self.filter_panel = None
        self.real_time_updater = RealTimeUpdater()
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("结直肠癌筛查模拟分析面板")
        self.setMinimumSize(1400, 900)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 左侧筛选面板
        self.filter_panel = FilterPanel()
        filter_dock = QDockWidget("筛选器", self)
        filter_dock.setWidget(self.filter_panel)
        filter_dock.setFeatures(QDockWidget.DockWidgetFeature.DockWidgetMovable | 
                               QDockWidget.DockWidgetFeature.DockWidgetFloatable)
        self.addDockWidget(Qt.DockWidgetArea.LeftDockWidgetArea, filter_dock)
        
        # 中央图表区域
        self.chart_area = QTabWidget()
        self.chart_area.setTabsClosable(True)
        self.chart_area.tabCloseRequested.connect(self.close_chart_tab)
        main_layout.addWidget(self.chart_area)
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建状态栏
        self.create_statusbar()
        
        # 加载默认视图
        self.load_default_view()
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = self.addToolBar("主工具栏")
        
        # 新建图表按钮
        new_chart_action = QAction("新建图表", self)
        new_chart_action.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_FileIcon))
        new_chart_action.triggered.connect(self.show_new_chart_dialog)
        toolbar.addAction(new_chart_action)
        
        toolbar.addSeparator()
        
        # 视图管理按钮
        view_menu = QMenu("视图", self)
        
        save_view_action = QAction("保存当前视图", self)
        save_view_action.triggered.connect(self.save_current_view)
        view_menu.addAction(save_view_action)
        
        load_view_action = QAction("加载视图", self)
        load_view_action.triggered.connect(self.show_load_view_dialog)
        view_menu.addAction(load_view_action)
        
        view_button = QToolButton()
        view_button.setText("视图")
        view_button.setMenu(view_menu)
        view_button.setPopupMode(QToolButton.ToolButtonPopupMode.InstantPopup)
        toolbar.addWidget(view_button)
        
        toolbar.addSeparator()
        
        # 导出按钮
        export_action = QAction("导出", self)
        export_action.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_DialogSaveButton))
        export_action.triggered.connect(self.show_export_dialog)
        toolbar.addAction(export_action)
        
        # 刷新按钮
        refresh_action = QAction("刷新", self)
        refresh_action.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_BrowserReload))
        refresh_action.triggered.connect(self.refresh_all_charts)
        toolbar.addAction(refresh_action)
```

### 交互式图表组件
```python
class InteractiveChartWidget(QWidget):
    def __init__(self, chart_type: str, data_source: str):
        super().__init__()
        self.chart_type = chart_type
        self.data_source = data_source
        self.chart_config = {}
        self.data = None
        self.setup_ui()
        
    def setup_ui(self):
        """设置图表界面"""
        layout = QVBoxLayout(self)
        
        # 图表控制面板
        control_panel = self.create_control_panel()
        layout.addWidget(control_panel)
        
        # 图表显示区域
        self.chart_view = QWebEngineView()
        layout.addWidget(self.chart_view)
        
        # 初始化图表
        self.initialize_chart()
    
    def create_control_panel(self) -> QWidget:
        """创建图表控制面板"""
        panel = QWidget()
        layout = QHBoxLayout(panel)
        
        # X轴选择
        layout.addWidget(QLabel("X轴:"))
        self.x_axis_combo = QComboBox()
        layout.addWidget(self.x_axis_combo)
        
        # Y轴选择
        layout.addWidget(QLabel("Y轴:"))
        self.y_axis_combo = QComboBox()
        layout.addWidget(self.y_axis_combo)
        
        # 分组选择
        layout.addWidget(QLabel("分组:"))
        self.group_combo = QComboBox()
        self.group_combo.addItem("无分组", None)
        layout.addWidget(self.group_combo)
        
        # 图表类型选择
        layout.addWidget(QLabel("图表类型:"))
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems(["柱状图", "折线图", "散点图", "箱线图", "热图"])
        self.chart_type_combo.currentTextChanged.connect(self.update_chart_type)
        layout.addWidget(self.chart_type_combo)
        
        # 更新按钮
        update_btn = QPushButton("更新图表")
        update_btn.clicked.connect(self.update_chart)
        layout.addWidget(update_btn)
        
        layout.addStretch()
        return panel
    
    def update_chart(self):
        """更新图表"""
        if self.data is None:
            return
        
        x_col = self.x_axis_combo.currentData()
        y_col = self.y_axis_combo.currentData()
        group_col = self.group_combo.currentData()
        chart_type = self.chart_type_combo.currentText()
        
        if not x_col or not y_col:
            return
        
        # 根据图表类型创建图表
        if chart_type == "柱状图":
            fig = self.create_bar_chart(x_col, y_col, group_col)
        elif chart_type == "折线图":
            fig = self.create_line_chart(x_col, y_col, group_col)
        elif chart_type == "散点图":
            fig = self.create_scatter_chart(x_col, y_col, group_col)
        elif chart_type == "箱线图":
            fig = self.create_box_chart(x_col, y_col, group_col)
        elif chart_type == "热图":
            fig = self.create_heatmap_chart(x_col, y_col)
        else:
            return
        
        # 添加交互功能
        fig.update_layout(
            title=f"{y_col} vs {x_col}",
            xaxis_title=x_col,
            yaxis_title=y_col,
            hovermode='closest',
            dragmode='zoom'
        )
        
        # 显示图表
        html_content = fig.to_html(include_plotlyjs='cdn')
        self.chart_view.setHtml(html_content)
    
    def create_bar_chart(self, x_col: str, y_col: str, group_col: Optional[str]) -> go.Figure:
        """创建柱状图"""
        if group_col:
            fig = px.bar(self.data, x=x_col, y=y_col, color=group_col,
                        title=f"{y_col} by {x_col} (grouped by {group_col})")
        else:
            fig = px.bar(self.data, x=x_col, y=y_col,
                        title=f"{y_col} by {x_col}")
        
        return fig
    
    def create_line_chart(self, x_col: str, y_col: str, group_col: Optional[str]) -> go.Figure:
        """创建折线图"""
        if group_col:
            fig = px.line(self.data, x=x_col, y=y_col, color=group_col,
                         title=f"{y_col} vs {x_col} (by {group_col})")
        else:
            fig = px.line(self.data, x=x_col, y=y_col,
                         title=f"{y_col} vs {x_col}")
        
        return fig
    
    def add_drill_down_capability(self):
        """添加钻取功能"""
        # 这里实现点击图表元素时的钻取逻辑
        js_code = """
        document.addEventListener('plotly_click', function(data) {
            var point = data.points[0];
            var drillDownData = {
                x: point.x,
                y: point.y,
                category: point.data.name
            };
            
            // 发送钻取请求到Python后端
            window.pywebview.api.handle_drill_down(drillDownData);
        });
        """
        
        self.chart_view.page().runJavaScript(js_code)
```

### 筛选面板实现
```python
class FilterPanel(QWidget):
    filter_changed = pyqtSignal(dict)  # 筛选条件变更信号
    
    def __init__(self):
        super().__init__()
        self.filter_widgets = {}
        self.current_filters = {}
        self.setup_ui()
        
    def setup_ui(self):
        """设置筛选面板界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("数据筛选器")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        layout.addWidget(title_label)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        self.filter_layout = QVBoxLayout(scroll_widget)
        
        # 添加各种筛选器
        self.add_date_range_filter()
        self.add_strategy_filter()
        self.add_age_group_filter()
        self.add_gender_filter()
        self.add_numeric_range_filters()
        
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        apply_btn = QPushButton("应用筛选")
        apply_btn.clicked.connect(self.apply_filters)
        button_layout.addWidget(apply_btn)
        
        reset_btn = QPushButton("重置")
        reset_btn.clicked.connect(self.reset_filters)
        button_layout.addWidget(reset_btn)
        
        layout.addLayout(button_layout)
    
    def add_date_range_filter(self):
        """添加日期范围筛选器"""
        group_box = QGroupBox("日期范围")
        layout = QVBoxLayout(group_box)
        
        # 开始日期
        start_layout = QHBoxLayout()
        start_layout.addWidget(QLabel("开始日期:"))
        start_date = QDateEdit()
        start_date.setCalendarPopup(True)
        start_date.setDate(QDate.currentDate().addYears(-1))
        start_layout.addWidget(start_date)
        layout.addLayout(start_layout)
        
        # 结束日期
        end_layout = QHBoxLayout()
        end_layout.addWidget(QLabel("结束日期:"))
        end_date = QDateEdit()
        end_date.setCalendarPopup(True)
        end_date.setDate(QDate.currentDate())
        end_layout.addWidget(end_date)
        layout.addLayout(end_layout)
        
        self.filter_widgets['date_range'] = {
            'start_date': start_date,
            'end_date': end_date
        }
        
        self.filter_layout.addWidget(group_box)
    
    def add_strategy_filter(self):
        """添加策略筛选器"""
        group_box = QGroupBox("筛查策略")
        layout = QVBoxLayout(group_box)
        
        # 策略列表（多选）
        strategy_list = QListWidget()
        strategy_list.setSelectionMode(QAbstractItemView.SelectionMode.MultiSelection)
        
        # 从数据库加载策略列表
        strategies = self.load_available_strategies()
        for strategy in strategies:
            item = QListWidgetItem(strategy)
            item.setCheckState(Qt.CheckState.Checked)
            strategy_list.addItem(item)
        
        layout.addWidget(strategy_list)
        
        self.filter_widgets['strategies'] = strategy_list
        self.filter_layout.addWidget(group_box)
    
    def add_numeric_range_filters(self):
        """添加数值范围筛选器"""
        numeric_fields = [
            ('总成本', 'total_cost', 0, 1000000),
            ('QALY', 'qalys', 0, 100),
            ('检出癌症数', 'cancers_detected', 0, 10000)
        ]
        
        for field_name, field_key, min_val, max_val in numeric_fields:
            group_box = QGroupBox(field_name)
            layout = QVBoxLayout(group_box)
            
            # 范围滑块
            range_slider = QSlider(Qt.Orientation.Horizontal)
            range_slider.setRange(min_val, max_val)
            range_slider.setValue(max_val)
            
            # 数值显示
            value_label = QLabel(f"≤ {max_val}")
            range_slider.valueChanged.connect(
                lambda v, label=value_label: label.setText(f"≤ {v}")
            )
            
            layout.addWidget(value_label)
            layout.addWidget(range_slider)
            
            self.filter_widgets[field_key] = {
                'slider': range_slider,
                'label': value_label
            }
            
            self.filter_layout.addWidget(group_box)
    
    def apply_filters(self):
        """应用筛选条件"""
        filters = {}
        
        # 日期范围筛选
        if 'date_range' in self.filter_widgets:
            start_date = self.filter_widgets['date_range']['start_date'].date().toPython()
            end_date = self.filter_widgets['date_range']['end_date'].date().toPython()
            filters['date_range'] = (start_date, end_date)
        
        # 策略筛选
        if 'strategies' in self.filter_widgets:
            strategy_list = self.filter_widgets['strategies']
            selected_strategies = []
            for i in range(strategy_list.count()):
                item = strategy_list.item(i)
                if item.checkState() == Qt.CheckState.Checked:
                    selected_strategies.append(item.text())
            filters['strategies'] = selected_strategies
        
        # 数值范围筛选
        for field_key in ['total_cost', 'qalys', 'cancers_detected']:
            if field_key in self.filter_widgets:
                max_value = self.filter_widgets[field_key]['slider'].value()
                filters[field_key] = {'max': max_value}
        
        self.current_filters = filters
        self.filter_changed.emit(filters)
```

### 实时数据更新器
```python
class RealTimeUpdater(QObject):
    data_updated = pyqtSignal(str, dict)  # 数据源, 更新数据
    
    def __init__(self):
        super().__init__()
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.check_for_updates)
        self.monitored_sources = {}
        self.update_interval = 30000  # 30秒
        
    def start_monitoring(self, data_source: str, update_callback: callable):
        """开始监控数据源"""
        self.monitored_sources[data_source] = {
            'callback': update_callback,
            'last_update': datetime.now(),
            'last_hash': None
        }
        
        if not self.update_timer.isActive():
            self.update_timer.start(self.update_interval)
    
    def check_for_updates(self):
        """检查数据更新"""
        for data_source, info in self.monitored_sources.items():
            try:
                # 获取最新数据
                latest_data = self.fetch_latest_data(data_source)
                
                # 计算数据哈希
                current_hash = self.calculate_data_hash(latest_data)
                
                # 检查是否有变化
                if current_hash != info['last_hash']:
                    info['last_hash'] = current_hash
                    info['last_update'] = datetime.now()
                    
                    # 发送更新信号
                    self.data_updated.emit(data_source, latest_data)
                    
                    # 调用回调函数
                    if info['callback']:
                        info['callback'](latest_data)
                        
            except Exception as e:
                print(f"Error checking updates for {data_source}: {e}")
    
    def fetch_latest_data(self, data_source: str) -> dict:
        """获取最新数据"""
        # 这里实现具体的数据获取逻辑
        # 可能从数据库、API或文件系统获取
        pass
    
    def calculate_data_hash(self, data: dict) -> str:
        """计算数据哈希值"""
        import hashlib
        import json
        
        data_str = json.dumps(data, sort_keys=True, default=str)
        return hashlib.md5(data_str.encode()).hexdigest()
```

### Testing
#### 测试文件位置
- `tests/unit/test_analysis_panel.py`
- `tests/unit/test_chart_widgets.py`
- `tests/unit/test_filter_panel.py`
- `tests/integration/test_interactive_visualization.py`

#### 测试标准
- 界面响应性和布局测试
- 图表交互功能测试
- 筛选和钻取功能测试
- 实时更新机制测试
- 导出功能完整性测试

#### 测试框架和模式
- 使用pytest-qt测试GUI组件
- Mock数据源测试图表生成
- 自动化UI测试验证交互功能
- 性能测试验证大数据集处理

#### 特定测试要求
- 界面响应时间: 所有操作 < 1秒
- 图表渲染性能: 1万数据点 < 3秒
- 内存使用: 多图表界面 < 2GB
- 实时更新延迟: < 5秒

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-31 | 1.0 | 初始故事创建 | Scrum Master |

## Dev Agent Record
*此部分将由开发代理在实施过程中填写*

### Agent Model Used
*待填写*

### Debug Log References
*待填写*

### Completion Notes List
*待填写*

### File List
*待填写*

## QA Results
*此部分将由QA代理在完成故事实施的QA审查后填写*
