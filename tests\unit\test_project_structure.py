"""
测试项目结构的基础测试

验证项目的基本结构和导入是否正确配置。
"""

import pytest
import sys
from pathlib import Path


class TestProjectStructure:
    """测试项目结构的基础功能"""

    def test_src_directory_exists(self):
        """测试src目录是否存在"""
        src_path = Path(__file__).parent.parent.parent / "src"
        assert src_path.exists(), "src目录应该存在"
        assert src_path.is_dir(), "src应该是一个目录"

    def test_core_modules_exist(self):
        """测试核心模块目录是否存在"""
        src_path = Path(__file__).parent.parent.parent / "src"
        
        expected_modules = [
            "core",
            "modules", 
            "interfaces",
            "services",
            "database",
            "utils"
        ]
        
        for module in expected_modules:
            module_path = src_path / module
            assert module_path.exists(), f"{module}模块目录应该存在"
            assert module_path.is_dir(), f"{module}应该是一个目录"
            
            init_file = module_path / "__init__.py"
            assert init_file.exists(), f"{module}模块应该有__init__.py文件"

    def test_src_import(self):
        """测试src模块是否可以正确导入"""
        try:
            import src
            assert hasattr(src, "__version__"), "src模块应该有__version__属性"
            assert src.__version__ == "0.1.0", "版本号应该是0.1.0"
        except ImportError as e:
            pytest.fail(f"无法导入src模块: {e}")

    def test_pyproject_toml_exists(self):
        """测试pyproject.toml文件是否存在"""
        project_root = Path(__file__).parent.parent.parent
        pyproject_path = project_root / "pyproject.toml"
        assert pyproject_path.exists(), "pyproject.toml文件应该存在"

    def test_tests_directory_structure(self):
        """测试测试目录结构是否正确"""
        tests_path = Path(__file__).parent.parent
        
        expected_test_dirs = ["unit", "integration", "performance", "fixtures"]
        
        for test_dir in expected_test_dirs:
            dir_path = tests_path / test_dir
            assert dir_path.exists(), f"测试目录{test_dir}应该存在"
