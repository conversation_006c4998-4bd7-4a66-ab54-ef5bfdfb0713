# Story 3.1: 筛查工具配置系统

## Status
Draft

## Story
**As a** 研究人员，
**I want** 配置多种筛查工具及其性能参数，
**so that** 准确模拟不同筛查方法的检测能力。

## Acceptance Criteria
1. 实现筛查工具枚举系统（FIT、结肠镜、乙状结肠镜、风险评估问卷，其他筛查工具）
2. 为每种工具配置疾病阶段特异性敏感性和特异性参数
3. 实现工具成本配置和管理系统
4. 添加筛查工具性能参数验证和范围检查
5. 创建筛查工具配置的导入/导出功能
6. 实现筛查工具性能的单元测试

## Tasks / Subtasks

- [ ] 任务1：实现筛查工具枚举和基础结构 (AC: 1)
  - [ ] 创建src/modules/screening/enums.py文件
  - [ ] 定义ScreeningToolType枚举（FIT、结肠镜、乙状结肠镜等）
  - [ ] 创建src/modules/screening/screening_tool.py文件
  - [ ] 实现ScreeningTool基础类，包含工具类型和基本属性
  - [ ] 添加工具特异性子类（FITTool、ColonoscopyTool等）
  - [ ] 实现工具注册和工厂模式

- [ ] 任务2：实现疾病阶段特异性性能参数 (AC: 2)
  - [ ] 扩展ScreeningTool类，添加敏感性和特异性配置
  - [ ] 实现疾病状态特异性敏感性映射
  - [ ] 添加解剖位置特异性检测能力
  - [ ] 创建性能参数插值和计算功能
  - [ ] 实现工具性能的年龄和性别调整
  - [ ] 添加性能参数的不确定性建模

- [ ] 任务3：实现工具成本配置和管理 (AC: 3)
  - [ ] 创建src/modules/screening/cost_model.py文件
  - [ ] 实现ScreeningCostModel类，管理工具成本
  - [ ] 添加直接成本配置（检查费用、材料费等）
  - [ ] 实现间接成本建模（时间成本、交通费等）
  - [ ] 创建成本参数的地区和时间调整
  - [ ] 添加成本配置的验证和范围检查

- [ ] 任务4：添加性能参数验证和范围检查 (AC: 4)
  - [ ] 扩展src/utils/validators.py，添加筛查工具验证
  - [ ] 实现敏感性和特异性范围验证（0-1之间）
  - [ ] 添加成本参数合理性检查
  - [ ] 创建工具配置完整性验证
  - [ ] 实现参数组合逻辑一致性检查
  - [ ] 添加配置变更影响分析

- [ ] 任务5：创建配置导入/导出功能 (AC: 5)
  - [ ] 创建data/screening_tools/目录结构
  - [ ] 设计筛查工具配置文件格式（YAML/JSON）
  - [ ] 实现配置文件加载和解析功能
  - [ ] 添加配置导出和序列化功能
  - [ ] 创建配置模板和示例文件
  - [ ] 实现配置版本管理和迁移

- [ ] 任务6：实现筛查工具性能测试套件 (AC: 6)
  - [ ] 创建tests/unit/test_screening_tools.py测试文件
  - [ ] 实现筛查工具创建和配置测试
  - [ ] 添加性能参数计算准确性测试
  - [ ] 创建成本计算验证测试
  - [ ] 实现配置导入/导出功能测试
  - [ ] 添加参数验证和错误处理测试

## Dev Notes

### 筛查工具枚举定义
```python
from enum import Enum
from dataclasses import dataclass
from typing import Dict, Optional

class ScreeningToolType(Enum):
    FIT = "fecal_immunochemical_test"           # 粪便免疫化学检测
    COLONOSCOPY = "colonoscopy"                 # 结肠镜检查
    SIGMOIDOSCOPY = "flexible_sigmoidoscopy"    # 乙状结肠镜检查
    RISK_QUESTIONNAIRE = "risk_assessment_questionnaire"  # 风险评估问卷
    CTCOLONOGRAPHY = "ct_colonography"          # CT结肠成像
    STOOL_DNA = "stool_dna_test"               # 粪便DNA检测
    CAPSULE_ENDOSCOPY = "capsule_endoscopy"    # 胶囊内镜

@dataclass
class ScreeningPerformance:
    sensitivity_by_state: Dict[DiseaseState, float]
    specificity: float
    detection_threshold: Optional[float] = None
    operator_dependency: float = 1.0
```

### 筛查工具配置文件格式
```yaml
# data/screening_tools/fit_tool_config.yaml
screening_tool:
  name: "粪便免疫化学检测 (FIT)"
  type: "FIT"
  version: "1.0"
  
  performance:
    specificity: 0.95
    sensitivity_by_state:
      normal: 0.0
      low_risk_adenoma: 0.15
      high_risk_adenoma: 0.35
      small_serrated_adenoma: 0.10
      large_serrated_adenoma: 0.25
      preclinical_cancer: 0.75
      clinical_cancer_stage_i: 0.85
      clinical_cancer_stage_ii: 0.90
      clinical_cancer_stage_iii: 0.95
      clinical_cancer_stage_iv: 0.98
  
  costs:
    direct_cost: 25.0        # 直接检查费用（元）
    material_cost: 5.0       # 材料费用
    processing_cost: 15.0    # 处理费用
    indirect_cost: 50.0      # 间接成本（时间、交通等）
  
  characteristics:
    invasiveness: "non_invasive"
    preparation_required: false
    operator_skill_required: "low"
    turnaround_time_days: 3
```

### 筛查工具基础类设计
```python
class ScreeningTool:
    def __init__(self, tool_type: ScreeningToolType, config: Dict):
        self.tool_type = tool_type
        self.config = config
        self.performance = self._load_performance_config(config)
        self.cost_model = ScreeningCostModel(config.get('costs', {}))
    
    def calculate_detection_probability(self, individual: Individual) -> float:
        """计算对特定个体的检测概率"""
        base_sensitivity = self.performance.sensitivity_by_state.get(
            individual.current_disease_state, 0.0
        )
        
        # 年龄调整
        age_factor = self._calculate_age_factor(individual.age)
        
        # 解剖位置调整
        location_factor = self._calculate_location_factor(individual.anatomical_location)
        
        return base_sensitivity * age_factor * location_factor
    
    def calculate_total_cost(self, individual: Individual) -> float:
        """计算筛查总成本"""
        return self.cost_model.calculate_total_cost(individual)
```

### 疾病状态特异性敏感性配置
```python
# 不同筛查工具的敏感性配置
SCREENING_TOOL_SENSITIVITIES = {
    ScreeningToolType.FIT: {
        DiseaseState.NORMAL: 0.0,
        DiseaseState.LOW_RISK_ADENOMA: 0.15,
        DiseaseState.HIGH_RISK_ADENOMA: 0.35,
        DiseaseState.PRECLINICAL_CANCER: 0.75,
        DiseaseState.CLINICAL_CANCER_STAGE_I: 0.85,
        DiseaseState.CLINICAL_CANCER_STAGE_IV: 0.98
    },
    ScreeningToolType.COLONOSCOPY: {
        DiseaseState.NORMAL: 0.0,
        DiseaseState.LOW_RISK_ADENOMA: 0.85,
        DiseaseState.HIGH_RISK_ADENOMA: 0.95,
        DiseaseState.PRECLINICAL_CANCER: 0.98,
        DiseaseState.CLINICAL_CANCER_STAGE_I: 0.99,
        DiseaseState.CLINICAL_CANCER_STAGE_IV: 1.0
    }
}
```

### 成本模型设计
```python
class ScreeningCostModel:
    def __init__(self, cost_config: Dict):
        self.direct_cost = cost_config.get('direct_cost', 0.0)
        self.material_cost = cost_config.get('material_cost', 0.0)
        self.processing_cost = cost_config.get('processing_cost', 0.0)
        self.indirect_cost = cost_config.get('indirect_cost', 0.0)
    
    def calculate_total_cost(self, individual: Individual) -> float:
        """计算个体筛查总成本"""
        base_cost = (self.direct_cost + self.material_cost + 
                    self.processing_cost + self.indirect_cost)
        
        # 年龄调整（老年人可能需要更多准备时间）
        age_multiplier = 1.0 + max(0, (individual.age - 65) * 0.01)
        
        return base_cost * age_multiplier
```

### 参数验证规则
- 敏感性和特异性: 0.0 ≤ 值 ≤ 1.0
- 成本参数: 值 ≥ 0.0
- 检测阈值: 如果适用，必须 > 0
- 操作者依赖性: 0.5 ≤ 值 ≤ 2.0
- 周转时间: 0 ≤ 天数 ≤ 30

### 工具性能基准值
- **FIT**: 特异性95%，癌症敏感性85%，腺瘤敏感性25%
- **结肠镜**: 特异性99%，癌症敏感性99%，腺瘤敏感性90%
- **乙状结肠镜**: 特异性98%，远端癌症敏感性95%，近端敏感性0%

### Testing
#### 测试文件位置
- `tests/unit/test_screening_tools.py`
- `tests/unit/test_screening_performance.py`
- `tests/unit/test_cost_model.py`
- `tests/integration/test_screening_config.py`

#### 测试标准
- 筛查工具创建和初始化测试
- 性能参数计算准确性验证
- 成本计算逻辑测试
- 配置文件加载和验证测试
- 参数范围和约束检查测试

#### 测试框架和模式
- 使用pytest fixtures提供测试配置
- 参数化测试验证不同工具类型
- Mock配置文件测试加载功能
- 数值精度测试验证计算准确性

#### 特定测试要求
- 性能计算精度: 误差 < 0.1%
- 配置加载时间: < 50ms
- 参数验证覆盖率: 100%
- 成本计算一致性: 重复计算结果相同

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-31 | 1.0 | 初始故事创建 | Scrum Master |

## Dev Agent Record
*此部分将由开发代理在实施过程中填写*

### Agent Model Used
*待填写*

### Debug Log References
*待填写*

### Completion Notes List
*待填写*

### File List
*待填写*

## QA Results
*此部分将由QA代理在完成故事实施的QA审查后填写*
