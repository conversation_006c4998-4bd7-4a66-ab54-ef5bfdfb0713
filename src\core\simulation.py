"""
模拟状态管理系统

定义SimulationState类，跟踪模拟进度、时间管理、
参数存储和事件调度机制。
"""

import json
import pickle
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable, Union
from dataclasses import dataclass, field
from pathlib import Path
import uuid

from .population import Population
from .individual import Individual


@dataclass
class SimulationEvent:
    """模拟事件类"""
    event_id: str
    event_time: float  # 模拟时间（年）
    event_type: str
    target_individual_id: Optional[str]
    event_data: Dict[str, Any] = field(default_factory=dict)
    priority: int = 0  # 优先级，数字越小优先级越高
    
    def __post_init__(self):
        """验证事件数据"""
        if self.event_time < 0:
            raise ValueError("事件时间不能为负数")
        if not self.event_type:
            raise ValueError("事件类型不能为空")


class EventQueue:
    """事件队列管理器"""
    
    def __init__(self):
        self.events: List[SimulationEvent] = []
    
    def add_event(self, event: SimulationEvent) -> None:
        """添加事件到队列"""
        self.events.append(event)
        # 按时间和优先级排序
        self.events.sort(key=lambda e: (e.event_time, e.priority))
    
    def get_next_event(self) -> Optional[SimulationEvent]:
        """获取下一个事件"""
        return self.events.pop(0) if self.events else None
    
    def peek_next_event(self) -> Optional[SimulationEvent]:
        """查看下一个事件但不移除"""
        return self.events[0] if self.events else None
    
    def get_events_at_time(self, time: float) -> List[SimulationEvent]:
        """获取指定时间的所有事件"""
        return [event for event in self.events if event.event_time == time]
    
    def remove_events_for_individual(self, individual_id: str) -> int:
        """移除指定个体的所有事件"""
        initial_count = len(self.events)
        self.events = [
            event for event in self.events 
            if event.target_individual_id != individual_id
        ]
        return initial_count - len(self.events)
    
    def clear(self) -> None:
        """清空事件队列"""
        self.events.clear()
    
    def size(self) -> int:
        """获取队列大小"""
        return len(self.events)
    
    def is_empty(self) -> bool:
        """检查队列是否为空"""
        return len(self.events) == 0


class SimulationState:
    """
    模拟状态管理类
    
    跟踪模拟进度、时间管理、参数存储和事件调度。
    """
    
    def __init__(
        self,
        simulation_id: Optional[str] = None,
        start_year: int = 2025,
        duration_years: int = 50,
        time_step: float = 1.0,
        random_seed: Optional[int] = None,
    ):
        """
        初始化模拟状态
        
        Args:
            simulation_id: 模拟唯一标识符
            start_year: 模拟开始年份
            duration_years: 模拟持续时间（年）
            time_step: 时间步长（年）
            random_seed: 随机种子
        """
        self.simulation_id = simulation_id or str(uuid.uuid4())
        self.start_year = start_year
        self.duration_years = duration_years
        self.time_step = time_step
        self.random_seed = random_seed
        
        # 时间管理
        self.current_time = 0.0  # 模拟时间（年）
        self.current_year = start_year
        self.is_running = False
        self.is_paused = False
        self.is_completed = False
        
        # 事件管理
        self.event_queue = EventQueue()
        
        # 参数存储
        self.parameters: Dict[str, Any] = {}
        self.results: Dict[str, Any] = {}
        
        # 统计信息
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        self.total_events_processed = 0
        
        # 回调函数
        self.event_handlers: Dict[str, List[Callable]] = {}
        
        # 验证初始参数
        self._validate_parameters()
    
    def _validate_parameters(self) -> None:
        """验证模拟参数"""
        if self.duration_years <= 0:
            raise ValueError("模拟持续时间必须大于0")
        if self.time_step <= 0:
            raise ValueError("时间步长必须大于0")
        if self.start_year < 1900:
            raise ValueError("开始年份不能早于1900年")
    
    def set_parameter(self, key: str, value: Any) -> None:
        """设置模拟参数"""
        self.parameters[key] = value
    
    def get_parameter(self, key: str, default: Any = None) -> Any:
        """获取模拟参数"""
        return self.parameters.get(key, default)
    
    def set_result(self, key: str, value: Any) -> None:
        """设置模拟结果"""
        self.results[key] = value
    
    def get_result(self, key: str, default: Any = None) -> Any:
        """获取模拟结果"""
        return self.results.get(key, default)
    
    def start_simulation(self) -> None:
        """开始模拟"""
        if self.is_running:
            raise RuntimeError("模拟已在运行中")
        
        self.is_running = True
        self.is_paused = False
        self.is_completed = False
        self.start_time = datetime.now()
        self.current_time = 0.0
        self.current_year = self.start_year
        
        # 设置随机种子
        if self.random_seed is not None:
            import numpy as np
            np.random.seed(self.random_seed)
    
    def pause_simulation(self) -> None:
        """暂停模拟"""
        if not self.is_running:
            raise RuntimeError("模拟未在运行")
        self.is_paused = True
    
    def resume_simulation(self) -> None:
        """恢复模拟"""
        if not self.is_running or not self.is_paused:
            raise RuntimeError("模拟未暂停")
        self.is_paused = False
    
    def stop_simulation(self) -> None:
        """停止模拟"""
        self.is_running = False
        self.is_paused = False
        self.is_completed = True
        self.end_time = datetime.now()
    
    def advance_time(self, time_increment: Optional[float] = None) -> None:
        """
        推进模拟时间
        
        Args:
            time_increment: 时间增量，默认使用time_step
        """
        if not self.is_running or self.is_paused:
            raise RuntimeError("模拟未运行或已暂停")
        
        increment = time_increment or self.time_step
        self.current_time += increment
        self.current_year = self.start_year + int(self.current_time)
        
        # 检查是否完成
        if self.current_time >= self.duration_years:
            self.stop_simulation()
    
    def schedule_event(
        self,
        event_type: str,
        delay: float,
        target_individual_id: Optional[str] = None,
        event_data: Optional[Dict[str, Any]] = None,
        priority: int = 0,
    ) -> str:
        """
        调度事件
        
        Args:
            event_type: 事件类型
            delay: 延迟时间（年）
            target_individual_id: 目标个体ID
            event_data: 事件数据
            priority: 优先级
            
        Returns:
            事件ID
        """
        event = SimulationEvent(
            event_id=str(uuid.uuid4()),
            event_time=self.current_time + delay,
            event_type=event_type,
            target_individual_id=target_individual_id,
            event_data=event_data or {},
            priority=priority,
        )
        
        self.event_queue.add_event(event)
        return event.event_id
    
    def process_next_event(self) -> Optional[SimulationEvent]:
        """处理下一个事件"""
        if self.event_queue.is_empty():
            return None
        
        event = self.event_queue.get_next_event()
        if event:
            # 更新时间到事件时间
            if event.event_time > self.current_time:
                self.current_time = event.event_time
                self.current_year = self.start_year + int(self.current_time)
            
            # 触发事件处理器
            self._trigger_event_handlers(event)
            self.total_events_processed += 1
        
        return event
    
    def register_event_handler(self, event_type: str, handler: Callable) -> None:
        """注册事件处理器"""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(handler)
    
    def _trigger_event_handlers(self, event: SimulationEvent) -> None:
        """触发事件处理器"""
        handlers = self.event_handlers.get(event.event_type, [])
        for handler in handlers:
            try:
                handler(event, self)
            except Exception as e:
                print(f"事件处理器错误: {e}")
    
    def get_progress(self) -> float:
        """获取模拟进度（0-1）"""
        return min(self.current_time / self.duration_years, 1.0)
    
    def get_remaining_time(self) -> float:
        """获取剩余时间（年）"""
        return max(0.0, self.duration_years - self.current_time)
    
    def get_elapsed_real_time(self) -> Optional[timedelta]:
        """获取实际经过的时间"""
        if self.start_time is None:
            return None
        
        end_time = self.end_time or datetime.now()
        return end_time - self.start_time
    
    def save_state(self, filepath: Union[str, Path]) -> None:
        """
        保存模拟状态到文件
        
        Args:
            filepath: 文件路径
        """
        filepath = Path(filepath)
        
        state_data = {
            "simulation_id": self.simulation_id,
            "start_year": self.start_year,
            "duration_years": self.duration_years,
            "time_step": self.time_step,
            "random_seed": self.random_seed,
            "current_time": self.current_time,
            "current_year": self.current_year,
            "is_running": self.is_running,
            "is_paused": self.is_paused,
            "is_completed": self.is_completed,
            "total_events_processed": self.total_events_processed,
            "parameters": self.parameters,
            "results": self.results,
            "events": [
                {
                    "event_id": event.event_id,
                    "event_time": event.event_time,
                    "event_type": event.event_type,
                    "target_individual_id": event.target_individual_id,
                    "event_data": event.event_data,
                    "priority": event.priority,
                }
                for event in self.event_queue.events
            ],
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(state_data, f, indent=2, ensure_ascii=False)
    
    @classmethod
    def load_state(cls, filepath: Union[str, Path]) -> "SimulationState":
        """
        从文件加载模拟状态
        
        Args:
            filepath: 文件路径
            
        Returns:
            模拟状态对象
        """
        filepath = Path(filepath)
        
        with open(filepath, 'r', encoding='utf-8') as f:
            state_data = json.load(f)
        
        # 创建新的模拟状态对象
        simulation = cls(
            simulation_id=state_data["simulation_id"],
            start_year=state_data["start_year"],
            duration_years=state_data["duration_years"],
            time_step=state_data["time_step"],
            random_seed=state_data["random_seed"],
        )
        
        # 恢复状态
        simulation.current_time = state_data["current_time"]
        simulation.current_year = state_data["current_year"]
        simulation.is_running = state_data["is_running"]
        simulation.is_paused = state_data["is_paused"]
        simulation.is_completed = state_data["is_completed"]
        simulation.total_events_processed = state_data["total_events_processed"]
        simulation.parameters = state_data["parameters"]
        simulation.results = state_data["results"]
        
        # 恢复事件队列
        for event_data in state_data["events"]:
            event = SimulationEvent(
                event_id=event_data["event_id"],
                event_time=event_data["event_time"],
                event_type=event_data["event_type"],
                target_individual_id=event_data["target_individual_id"],
                event_data=event_data["event_data"],
                priority=event_data["priority"],
            )
            simulation.event_queue.add_event(event)
        
        return simulation
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "simulation_id": self.simulation_id,
            "current_time": self.current_time,
            "current_year": self.current_year,
            "progress": self.get_progress(),
            "is_running": self.is_running,
            "is_paused": self.is_paused,
            "is_completed": self.is_completed,
            "total_events_processed": self.total_events_processed,
            "events_in_queue": self.event_queue.size(),
            "parameters_count": len(self.parameters),
            "results_count": len(self.results),
        }
    
    def __repr__(self) -> str:
        """字符串表示"""
        return (
            f"SimulationState(id={self.simulation_id[:8]}..., "
            f"time={self.current_time:.1f}/{self.duration_years}, "
            f"progress={self.get_progress():.1%})"
        )
