# 结直肠癌筛查微观模拟模型 - 桌面应用开发提示集

## 🎯 项目概述提示（用于桌面应用开发）

```
请为结直肠癌筛查微观模拟模型创建一个专业的科学研究桌面应用。这是一个面向医疗政策制定者、流行病学专家和卫生经济学家的复杂数据分析平台。

核心特点：
- 科学研究级别的专业桌面界面
- 复杂参数配置的渐进式披露
- 大规模数据可视化（支持100万个体）
- 机器学习校准过程监控
- 长期模拟执行监控（100年周期）
- 全面的卫生经济学分析

技术要求：
- 使用Python + PyQt6/PySide6 或 Tkinter
- 跨平台支持（Windows、macOS、Linux）
- WCAG 2.1 AA无障碍合规
- 高性能数据可视化（Matplotlib、PyQtGraph）
- 模块化组件架构
- SQLite本地数据库

设计原则：
- 科学可信度优先 - 专业桌面界面建立对复杂分析结果的信心
- 渐进式披露 - 复杂参数按逻辑工作流组织，避免认知过载
- 透明度至上 - 清晰展示模型假设、计算过程和数据来源
- 可重现性支持 - 便于保存、记录和重复模拟配置

色彩方案：
- 主色调：#1A365D（深蓝色）
- 辅助色：#4A90E2（浅蓝色）
- 强调色：#F5A623（橙色）
- 成功色：#2ECC71（绿色）
- 警告色：#F39C12（黄色）
- 错误色：#E74C3C（红色）
- 中性色：#333333, #666666, #EEEEEE

字体：
- 主要字体：系统默认字体（Windows: Segoe UI, macOS: SF Pro, Linux: Ubuntu）
- 等宽字体：Consolas/Monaco/Ubuntu Mono（代码、数据显示）
```

## 🏠 主界面开发提示

```
创建结直肠癌筛查模拟系统的主桌面应用界面，包含以下元素：

布局结构：
- 顶部菜单栏：文件、编辑、视图、工具、帮助（原生菜单栏）
- 工具栏：新建、打开、保存、导出、设置等快捷按钮
- 左侧导航面板：项目管理、人群设置、疾病进展、筛查策略、校准设置、模拟运行、结果分析
- 中央内容区域：当前功能的主要工作区域
- 右侧属性面板：当前选中项目的详细信息和快速操作
- 底部状态栏：进度信息、系统状态、内存使用等

关键组件：

1. 项目管理面板
   - 项目列表（树形结构）
   - 项目状态指示器（进行中/已完成/草稿）
   - 项目信息预览（模拟人数、运行时间、最后更新）
   - 右键上下文菜单：打开、复制、删除、导出
   - 双击打开项目

2. 工具栏快速操作
   - 新建项目（主要按钮，深蓝色背景）
   - 打开项目（文件对话框）
   - 保存项目（Ctrl+S快捷键）
   - 导入数据文件（支持拖拽到窗口）
   - 导出结果（多格式选择）
   - 应用设置

3. 状态监控区域
   - 当前运行的模拟进度条
   - 系统资源使用率（CPU、内存、磁盘）
   - 后台任务列表
   - 最近操作历史
   - 错误和警告通知

设计要求：
- 原生桌面应用外观和感觉
- 清晰的视觉层次，合理的间距
- 可调整的面板大小（分割器）
- 加载状态指示器和空状态处理
- 实时数据更新指示器

技术实现：
- 使用PyQt6/PySide6的布局管理器
- QListWidget/QTreeWidget实现虚拟滚动
- QThread用于后台任务和实时更新
- QSettings保存用户偏好设置
- SQLite数据库缓存
```

## ⚙️ 模拟配置向导提示

```
创建一个6步模拟配置向导对话框，用于设置复杂的结直肠癌筛查模拟参数：

步骤结构：
1. 人群设置（人口统计学、生命表导入）
2. 疾病建模（双通路设置、风险因素）
3. 筛查策略（工具配置、时间线设计）
4. 经济参数（成本设置、经济指标）
5. 校准设置（基准值、机器学习配置）
6. 模拟执行（参数确认、启动模拟）

界面布局：
- 顶部：步骤进度指示器（QProgressBar，带完成状态标识）
- 中央：QStackedWidget切换不同步骤的配置页面
- 左侧：参数配置区域（QScrollArea包含表单控件）
- 右侧：预览面板（QTextEdit显示配置摘要）
- 底部：按钮栏（上一步、下一步、保存草稿、取消、完成）

关键功能：

1. 实时参数验证
   - QLineEdit样式表标示错误字段（红色边框）
   - QLabel显示错误消息和修正建议
   - 参数冲突检测和警告（QMessageBox）
   - 必填字段标记（红色星号标签）

2. 智能帮助系统
   - QToolButton触发上下文帮助（QToolTip）
   - QTextBrowser显示医学文献参考
   - 默认值建议和合理范围提示
   - 参数影响说明（QLabel）

3. 数据导入集成
   - 拖拽区域（QFrame，接受拖拽事件）
   - 支持CSV、Excel、JSON格式
   - 文件格式验证和错误提示
   - 数据预览表格（QTableWidget，前10行）
   - 列映射工具（QComboBox选择）

4. 配置预览
   - 参数摘要（QGroupBox分组显示）
   - 预估运行时间和资源需求
   - 配置完整性检查（QProgressBar）
   - 关键假设和限制说明

特殊组件：

1. 风险因素权重滑块
   - 6个QSlider：家族史、IBD、肥胖、糖尿病、吸烟、久坐
   - 0-3倍权重范围，默认值基于文献
   - 实时影响预览（QCustomPlot小图表）

2. 筛查策略时间线
   - 自定义QWidget时间轴（50-80岁）
   - 筛查工具图标（QIcon，FIT、结肠镜等）
   - 间隔设置（QSpinBox年份输入）
   - 重叠检测和冲突警告

技术实现：
- QStateMachine管理步骤流程
- QValidator进行表单验证
- QFileDialog和拖拽事件处理文件上传
- QTableWidget显示数据表格
- QCustomPlot或Matplotlib集成显示图表
- 自定义拖拽功能（QDrag和QMimeData）
```

## 📊 数据可视化组件提示

```
创建专业的科学数据可视化组件库，支持大规模数据展示：

核心图表组件：

1. 科学线图组件 (使用QCustomPlot或Matplotlib)
   - 支持多条数据线（最多10条）
   - X轴：时间或年龄，Y轴：发病率/死亡率
   - 可缩放和平移（鼠标滚轮、拖拽）
   - 数据点悬停详情（QToolTip）
   - 置信区间阴影（半透明填充）
   - 图例（可点击隐藏/显示线条）
   - 导出功能（PNG、SVG、PDF按钮）
   - 网格线和坐标轴标签

2. 交互式柱状图 (QCustomPlot)
   - 分组和堆叠模式切换
   - 动态排序（升序/降序）
   - 数据筛选（QCheckBox）
   - 数据钻取功能（点击展开详情）
   - 比较模式（并排显示多个数据集）
   - 动画过渡效果

3. 参数敏感性热力图 (QCustomPlot热力图)
   - 参数名称作为行列标签
   - 颜色渐变映射敏感性强度
   - QToolTip显示具体数值
   - 行列排序和分组
   - 颜色图例和数值范围

4. 成本效益散点图 (QCustomPlot散点图)
   - X轴：增量效果，Y轴：增量成本
   - 气泡大小映射人群规模
   - 象限分割线（成本效益阈值）
   - 数据点选择和高亮
   - 策略标签和图例

5. 模拟进度监控 (QProgressBar + QLabel)
   - 环形进度指示器（年份进度）
   - QTimer实时更新
   - 多层进度显示（总体/当前阶段）
   - 预估完成时间显示
   - QPushButton暂停/恢复控制

性能要求：
- 支持10万+数据点渲染
- 使用原生图形库加速（OpenGL）
- 虚拟化长列表（QAbstractItemModel）
- 数据分页和懒加载
- 内存使用优化（及时清理）

交互特性：
- 图表联动筛选（信号槽机制）
- 缩放和平移（鼠标事件处理）
- 数据选择和高亮
- 实时数据更新（QThread）
- 多显示器支持

技术实现：
- 使用QCustomPlot进行复杂可视化
- OpenGL渲染大数据集
- 信号槽优化更新机制
- QThread后台线程处理数据计算
- SQLite缓存大数据集
- QTimer优化刷新频率
```

## 🤖 机器学习界面提示

```
创建机器学习校准监控窗口，用于深度神经网络训练过程：

主要区域布局：

1. 训练配置面板（QGroupBox，左上区域）
   - 网络架构选择（QComboBox：MLP、CNN、RNN）
   - 超参数设置：
     * 学习率（QSlider，0.001-0.1，对数刻度）
     * 批次大小（QComboBox：32、64、128、256）
     * 隐藏层数（QSlider，1-5层）
     * 神经元数量（QSpinBox每层，64-512）
   - 训练数据配置：
     * 拉丁超立方抽样参数
     * 训练/验证集比例（QSlider，80/20默认）
   - 校准目标权重设置（多个QSlider）

2. 实时监控面板（QCustomPlot，右上区域）
   - 损失函数曲线（双Y轴：训练损失vs验证损失）
   - 参数收敛状态图（散点图显示参数变化）
   - 训练速度指标（QLabel，每秒样本数，QTimer实时更新）
   - 资源使用监控：
     * GPU利用率（QProgressBar，绿色）
     * 内存使用（QProgressBar，蓝色）
     * CPU利用率（QProgressBar，橙色）

3. 结果分析面板（QGroupBox，左下区域）
   - 校准精度指标（QLabel显示R²、RMSE、MAE）
   - 置信区间计算结果（QTextEdit显示95%CI）
   - 参数分布可视化（QCustomPlot直方图）
   - 收敛诊断图表（QCustomPlot梯度范数）

4. 控制操作面板（QGroupBox，右下区域）
   - 训练控制按钮：
     * 开始训练（QPushButton，绿色样式）
     * 暂停/恢复（QPushButton，黄色样式）
     * 停止训练（QPushButton，红色样式）
   - 模型管理：
     * 保存检查点（QPushButton，自动+手动）
     * 加载预训练模型（QFileDialog）
     * 导出训练模型（QFileDialog）
   - 超参数实时调整（训练中可调）

特殊功能：

1. 早停建议系统
   - 自动检测过拟合（验证损失上升）
   - 建议最佳停止点（高亮显示）
   - 模型性能预测（趋势线）

2. 超参数优化助手
   - 自动调参建议（基于当前性能）
   - 网格搜索可视化（热力图）
   - 贝叶斯优化集成

3. 训练日志系统
   - 详细的训练历史（表格形式）
   - 错误和警告记录（红色/黄色标记）
   - 性能瓶颈分析（图表）
   - 导出日志功能

视觉设计：
- 深色主题（适合长时间监控）
- 实时更新的图表（平滑动画）
- 状态指示器（绿色=正常，红色=错误）
- 进度条和百分比显示

技术实现：
- QThread进程间通信实时数据流
- QTimer高频率图表更新优化
- QSettings训练状态持久化
- QThreadPool后台任务管理
- 错误恢复机制（自动重连）
```

## �️ 桌面布局适配提示

```
实现桌面应用的多屏幕尺寸适配：

屏幕尺寸策略：
- 小屏幕：1366x768（紧凑布局）
- 标准屏幕：1920x1080（标准布局）
- 大屏幕：2560x1440+（扩展布局）
- 多显示器：支持跨屏幕拖拽

布局适配规则：

小屏幕优化（1366x768）：
- 可折叠侧边栏（QSplitter）
- 紧凑的工具栏
- 简化的图表显示（隐藏次要信息）
- 标签页式内容组织
- 滚动区域处理内容溢出
- 最小窗口尺寸限制

标准屏幕适配（1920x1080）：
- 固定侧边导航（QDockWidget）
- 多列数据展示（QTableWidget）
- 完整功能的图表
- 键盘快捷键支持（QShortcut）
- 悬停效果和工具提示（QToolTip）
- 右键上下文菜单（QMenu）

大屏幕增强（2560x1440+）：
- 并排比较视图（QSplitter）
- 多面板同时显示（QMdiArea）
- 扩展的数据表格（更多列）
- 高密度信息展示
- 分屏功能（多窗口）
- 多显示器支持

多显示器支持：
- 窗口拖拽到不同显示器
- 记住窗口位置（QSettings）
- 全屏模式支持
- 显示器分辨率自适应

PyQt实现示例：
```python
class AdaptiveMainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setupUI()
        self.setupLayouts()

    def setupUI(self):
        # 获取屏幕尺寸
        screen = QApplication.primaryScreen()
        screen_size = screen.size()

        # 根据屏幕尺寸调整布局
        if screen_size.width() < 1400:
            self.setupCompactLayout()
        elif screen_size.width() < 1920:
            self.setupStandardLayout()
        else:
            self.setupExpandedLayout()

    def setupCompactLayout(self):
        # 紧凑布局：可折叠侧边栏
        self.sidebar.setMaximumWidth(200)
        self.toolbar.setToolButtonStyle(Qt.ToolButtonIconOnly)

    def setupStandardLayout(self):
        # 标准布局：固定侧边栏
        self.sidebar.setMaximumWidth(300)
        self.toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)

    def setupExpandedLayout(self):
        # 扩展布局：多面板显示
        self.sidebar.setMaximumWidth(350)
        self.enableMultiPanel()
```

技术实现：
- QSplitter实现可调整布局
- QDockWidget支持面板拖拽
- QSettings保存布局状态
- QShortcut键盘导航优化
- QScreen监控显示器变化
```

## ♿ 无障碍访问提示

```
确保界面符合WCAG 2.1 AA标准，支持所有用户：

视觉无障碍要求：
- 色彩对比度至少4.5:1（正常文本）
- 大文本（18px+）对比度至少3:1
- 不依赖颜色传达信息（使用图标+文字）
- 支持200%文本缩放而不丢失功能
- 高对比度模式支持
- 焦点指示器清晰可见（2px蓝色边框）

键盘导航要求：
- 所有交互元素可通过Tab键访问
- 逻辑的Tab顺序（从左到右，从上到下）
- Shift+Tab反向导航
- Enter和Space键激活按钮
- 箭头键导航菜单和列表
- Escape键关闭模态框

快捷键支持：
- Alt+1: 跳转到主内容
- Alt+2: 跳转到导航
- Alt+3: 跳转到搜索
- Ctrl+S: 保存当前配置
- Ctrl+N: 新建项目

屏幕阅读器支持：
- 控件可访问性设置（setAccessibleName()、setAccessibleDescription()）
- 完整的可访问性属性：
  * setAccessibleName(): 描述控件用途
  * setAccessibleDescription(): 关联帮助文本
  * QAccessible.State: 控件状态信息
  * QAccessible.updateAccessibility(): 动态内容更新通知
- 图表的替代文本描述
- 表格的标题和摘要（QTableWidget.setHorizontalHeaderLabels()）
- 表单标签正确关联（QLabel.setBuddy()）

认知无障碍：
- 清晰的错误消息（具体说明问题和解决方法）
- 一致的交互模式（相同操作在不同页面表现一致）
- 操作确认和撤销机制
- 进度指示和反馈
- 简洁的语言和术语解释

PyQt示例：
```python
# 无障碍设计示例
class AccessibleConfigWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupUI()

    def setupUI(self):
        layout = QVBoxLayout(self)

        # 标题
        title = QLabel("模拟配置")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAccessibleName("页面标题")
        layout.addWidget(title)

        # 表单区域
        formGroup = QGroupBox("基本参数")
        formLayout = QFormLayout()

        # 人群规模输入
        popLabel = QLabel("人群规模:")
        popInput = QSpinBox()
        popInput.setRange(10000, 1000000)
        popInput.setAccessibleName("人群规模输入框")
        popInput.setAccessibleDescription("请输入10,000到1,000,000之间的数值")
        popLabel.setBuddy(popInput)  # 关联标签和控件

        helpText = QLabel("请输入10,000到1,000,000之间的数值")
        helpText.setStyleSheet("color: gray; font-size: 11px;")

        formLayout.addRow(popLabel, popInput)
        formLayout.addRow("", helpText)

        # 状态更新区域
        self.statusLabel = QLabel("准备就绪")
        self.statusLabel.setAccessibleName("状态更新")
        self.statusLabel.setAccessibleDescription("显示当前操作状态")

        # 图表区域（带替代文本）
        chartWidget = QWidget()
        chartWidget.setAccessibleName("年龄特异性发病率趋势图表")
        chartWidget.setAccessibleDescription(
            "图表显示50-80岁人群的结直肠癌发病率随年龄增长而上升，"
            "从50岁的0.1%增长到80岁的2.3%"
        )

        formGroup.setLayout(formLayout)
        layout.addWidget(formGroup)
        layout.addWidget(self.statusLabel)
        layout.addWidget(chartWidget)

    def updateStatus(self, message):
        """更新状态并通知屏幕阅读器"""
        self.statusLabel.setText(message)
        QAccessible.updateAccessibility(self.statusLabel)
```

技术实现：
- 使用QAccessible API设置可访问性属性
- 实现动态内容更新通知
- 提供键盘快捷键（QShortcut）
- 测试屏幕阅读器兼容性（NVDA、JAWS）
- 键盘导航管理（setTabOrder()）
- 高对比度模式支持
```

## ⚡ 性能优化提示

```
实现高性能的大数据处理界面，支持100万个体数据：

数据处理优化：
- 虚拟滚动大列表（QAbstractItemModel + QListView）
- 数据分页和懒加载（每页1000条记录）
- QThread处理计算密集任务
- SQLite缓存大数据集（本地存储）
- 数据压缩和增量更新（只处理变化部分）

渲染优化：
- 信号槽连接优化防止不必要更新
- 缓存计算结果（Python @lru_cache）
- 延迟初始化组件
- 模块化加载（Python importlib）
- 图片懒加载和优化格式

内存管理：
- 及时断开信号槽连接
- QTimer清理和对象销毁
- 大数据集的流式处理
- 内存使用监控和预警
- Python垃圾回收优化

本地存储优化：
- SQLite查询优化和索引
- 数据库连接池管理
- 文件系统缓存策略
- 配置文件压缩存储
- 临时文件清理机制

大数据可视化优化：
- QCustomPlot硬件加速渲染
- OpenGL加速（QOpenGLWidget）
- 数据聚合和采样显示
- LOD（细节层次）技术
- 视口裁剪（只渲染可见部分）

代码示例：
```python
# 虚拟滚动实现
class VirtualizedListModel(QAbstractListModel):
    def __init__(self, data, parent=None):
        super().__init__(parent)
        self._data = data

    def rowCount(self, parent=QModelIndex()):
        return len(self._data)

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid():
            return None
        if role == Qt.DisplayRole:
            item = self._data[index.row()]
            return f"{item['name']} - {item['value']}"
        return None

class VirtualizedListView(QListView):
    def __init__(self, data, parent=None):
        super().__init__(parent)
        self.model = VirtualizedListModel(data)
        self.setModel(self.model)

# QThread数据处理
class DataProcessorThread(QThread):
    dataProcessed = pyqtSignal(object)

    def __init__(self, data):
        super().__init__()
        self.data = data

    def run(self):
        processed_data = self.process_data(self.data)
        self.dataProcessed.emit(processed_data)

    def process_data(self, data):
        # 实际的数据处理逻辑
        return [item for item in data if item['value'] > 0]

# 内存优化的图表组件
class OptimizedChartWidget(QWidget):
    def __init__(self, data, width, height, parent=None):
        super().__init__(parent)
        self.original_data = data
        self.width = width
        self.height = height
        self.setupChart()

    def setupChart(self):
        # 数据采样，最多显示1000个点
        if len(self.original_data) > 1000:
            step = len(self.original_data) // 1000
            processed_data = self.original_data[::step]
        else:
            processed_data = self.original_data

        # 创建图表
        self.chart = QCustomPlot(self)
        self.chart.addGraph()
        self.chart.graph(0).setData(processed_data)

        layout = QVBoxLayout(self)
        layout.addWidget(self.chart)
```

监控和调试：
- 性能指标收集（Python性能分析）
- 错误处理和日志（Python logging）
- 开发工具集成（PyQt调试工具）
- 用户体验监控（应用使用统计）
- 内存分析（memory_profiler）
```

## 🔧 开发工具配置提示

```
推荐的桌面应用开发环境和工具配置：

项目初始化：
```bash
# 创建Python项目
mkdir colorectal-screening-app
cd colorectal-screening-app

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装核心依赖
pip install PyQt6 PyQt6-tools
pip install matplotlib seaborn plotly
pip install pandas numpy scipy
pip install scikit-learn tensorflow
pip install SQLAlchemy sqlite3

# 安装开发依赖
pip install pytest pytest-qt pytest-cov
pip install black flake8 mypy
pip install pre-commit bandit
pip install pyinstaller cx_Freeze
```

项目配置（pyproject.toml）：
```toml
[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "colorectal-screening-model"
version = "1.0.0"
description = "结直肠癌筛查微观模拟模型桌面应用"
authors = [{name = "Your Name", email = "<EMAIL>"}]
license = {text = "MIT"}
dependencies = [
    "PyQt6>=6.4.0",
    "matplotlib>=3.6.0",
    "pandas>=1.5.0",
    "numpy>=1.24.0",
    "scikit-learn>=1.2.0",
    "SQLAlchemy>=2.0.0"
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-qt>=4.2.0",
    "black>=22.0.0",
    "flake8>=5.0.0",
    "mypy>=1.0.0"
]

[tool.black]
line-length = 88
target-version = ['py39']

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
```

推荐的IDE扩展（PyCharm/VS Code）：
- Python
- PyQt Integration
- Black Formatter
- Pylance (Type Checker)
- Python Docstring Generator
- GitLens
- SQLite Viewer
```

---

## 📝 文档更新记录

**版本 2.0** (2025-01-31)
- 将Web应用开发提示完全转换为桌面应用开发提示
- 替换React/JavaScript技术栈为PyQt/Python技术栈
- 更新所有代码示例和配置文件
- 调整性能优化策略适配桌面应用
- 修改无障碍设计实现方案
- 更新开发工具和环境配置

**版本 1.0** (初始版本)
- 完成Web应用AI前端开发提示集

---

**文档状态**：已更新为桌面应用版本
**负责人**：UX专家 Sally
**最后更新**：2025-01-31
