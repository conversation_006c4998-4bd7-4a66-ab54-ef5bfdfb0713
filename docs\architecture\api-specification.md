# API 规格

## 核心API端点设计

### 模拟管理API
```python
# 创建新模拟
POST /api/simulations
{
    "name": "筛查策略比较研究",
    "population_config": {...},
    "disease_model_config": {...},
    "screening_strategies": [...],
    "economic_parameters": {...},
    "simulation_parameters": {
        "duration_years": 100,
        "time_step_months": 3
    }
}

# 启动模拟执行
POST /api/simulations/{id}/run
{
    "parallel_processes": 4,
    "memory_limit_gb": 8,
    "checkpoint_interval": 1000
}

# 获取模拟状态
GET /api/simulations/{id}/status
{
    "status": "running",
    "progress_percent": 45.2,
    "current_year": 45,
    "estimated_completion": "2025-02-01T15:30:00Z",
    "memory_usage_gb": 6.2,
    "cpu_usage_percent": 85.0
}
```

### 数据管理API
```python
# 导入生命表数据
POST /api/data/life-tables
Content-Type: multipart/form-data
{
    "file": <CSV文件>,
    "region": "中国",
    "year": 2020,
    "source": "国家统计局"
}

# 导入校准基准值
POST /api/data/calibration-targets
{
    "adenoma_prevalence": {
        "age_50_54": {"male": 0.15, "female": 0.12},
        "age_55_59": {"male": 0.18, "female": 0.14}
    },
    "cancer_incidence": {...},
    "source": "中国癌症统计2020"
}
```

### 校准API
```python
# 启动机器学习校准
POST /api/calibration/start
{
    "parameter_ranges": {
        "adenoma_initiation_rate": [0.001, 0.01],
        "progression_multiplier": [0.8, 1.2]
    },
    "sampling_method": "latin_hypercube",
    "sample_size": 10000,
    "neural_network_config": {
        "hidden_layers": [128, 64, 32],
        "activation": "relu",
        "learning_rate": 0.001
    }
}

# 获取校准进度
GET /api/calibration/{id}/progress
{
    "status": "training",
    "epoch": 150,
    "total_epochs": 500,
    "training_loss": 0.0234,
    "validation_loss": 0.0267,
    "convergence_status": "improving"
}
```

### 结果分析API
```python
# 获取模拟结果摘要
GET /api/results/{simulation_id}/summary
{
    "population_outcomes": {
        "total_individuals": 1000000,
        "cancer_cases_prevented": 15420,
        "deaths_prevented": 8930,
        "life_years_gained": 89300
    },
    "economic_outcomes": {
        "total_screening_costs": 45000000,
        "total_treatment_costs": 120000000,
        "qalys_gained": 67200,
        "icer": 2455.30
    }
}

# 导出详细结果
POST /api/results/{simulation_id}/export
{
    "format": "csv",
    "include_individual_data": false,
    "aggregation_level": "yearly",
    "metrics": ["incidence", "mortality", "costs", "qalys"]
}
```
