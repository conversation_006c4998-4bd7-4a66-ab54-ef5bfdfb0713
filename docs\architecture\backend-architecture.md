# 后端架构

## 核心模拟引擎

### 模拟引擎架构

```python
class MicrosimulationEngine:
    """微观模拟引擎核心类"""
    
    def __init__(self, config: SimulationConfig):
        self.config = config
        self.population = None
        self.disease_model = DiseaseModel()
        self.screening_model = ScreeningModel()
        self.economic_model = EconomicModel()
        self.calibration_engine = CalibrationEngine()
        
    def initialize_population(self, population_config: PopulationConfig):
        """初始化人群队列"""
        self.population = Population.create_from_config(population_config)
        
    def run_simulation(self, duration_years: int) -> SimulationResults:
        """运行完整模拟"""
        results = SimulationResults()
        
        for year in range(duration_years):
            # 年度模拟循环
            self.advance_population_one_year(year)
            self.apply_screening_interventions(year)
            self.calculate_economic_outcomes(year)
            
            # 记录年度结果
            results.record_yearly_outcomes(year, self.population)
            
        return results
```

## 疾病进展建模

### 双通路疾病模型

```python
class DiseaseModel:
    """疾病进展模型"""
    
    def __init__(self):
        self.adenoma_pathway = AdenomaCarcinomaPathway()
        self.serrated_pathway = SerratedAdenomaPathway()
        
    def advance_individual_disease_state(self, individual: Individual, 
                                       time_step: int) -> None:
        """推进个体疾病状态"""
        if individual.pathway_type == PathwayType.ADENOMA_CARCINOMA:
            self.adenoma_pathway.advance_state(individual, time_step)
        else:
            self.serrated_pathway.advance_state(individual, time_step)

class AdenomaCarcinomaPathway:
    """腺瘤-癌变通路（85%病例）"""
    
    def advance_state(self, individual: Individual, time_step: int):
        """推进腺瘤-癌变通路状态"""
        current_state = individual.current_disease_state
        
        if current_state == DiseaseState.NORMAL:
            self.check_adenoma_initiation(individual, time_step)
        elif current_state == DiseaseState.LOW_RISK_ADENOMA:
            self.check_adenoma_progression(individual, time_step)
        elif current_state == DiseaseState.HIGH_RISK_ADENOMA:
            self.check_cancer_progression(individual, time_step)
        # ... 其他状态转换逻辑

class SerratedAdenomaPathway:
    """锯齿状腺瘤通路（15%病例）"""
    
    def advance_state(self, individual: Individual, time_step: int):
        """推进锯齿状腺瘤通路状态"""
        current_state = individual.current_disease_state
        
        if current_state == DiseaseState.NORMAL:
            self.check_serrated_initiation(individual, time_step)
        elif current_state == DiseaseState.SMALL_SERRATED:
            self.check_serrated_progression(individual, time_step)
        # ... 其他状态转换逻辑
```

## 筛查模拟引擎

### 筛查工具模型

```python
class ScreeningModel:
    """筛查模拟模型"""
    
    def __init__(self):
        self.tools = {
            ScreeningTool.FIT: FITScreeningTool(),
            ScreeningTool.COLONOSCOPY: ColonoscopyTool(),
            ScreeningTool.SIGMOIDOSCOPY: SigmoidoscopyTool(),
            ScreeningTool.RISK_ASSESSMENT: RiskAssessmentTool()
        }
        
    def apply_screening_strategy(self, population: Population, 
                               strategy: ScreeningStrategy, 
                               current_time: int) -> List[ScreeningEvent]:
        """应用筛查策略到人群"""
        events = []
        
        for individual in population.individuals:
            if self.is_eligible_for_screening(individual, strategy, current_time):
                event = self.perform_screening(individual, strategy, current_time)
                if event:
                    events.append(event)
                    
        return events

class FITScreeningTool:
    """粪便免疫化学检测工具"""
    
    def __init__(self):
        self.sensitivity = {
            DiseaseState.LOW_RISK_ADENOMA: 0.15,
            DiseaseState.HIGH_RISK_ADENOMA: 0.25,
            DiseaseState.PRECLINICAL_CANCER: 0.75,
            DiseaseState.CLINICAL_CANCER: 0.85
        }
        self.specificity = 0.95
        self.cost = 50.0  # 人民币
```

## 机器学习校准系统

### 神经网络校准

```python
class CalibrationEngine:
    """机器学习校准引擎"""
    
    def __init__(self):
        self.neural_network = None
        self.parameter_sampler = LatinHypercubeSampler()
        
    def calibrate_model(self, targets: List[CalibrationTarget], 
                       parameter_ranges: Dict[str, Tuple[float, float]]) -> CalibrationResults:
        """执行模型校准"""
        
        # 1. 生成参数样本
        parameter_samples = self.parameter_sampler.generate_samples(
            parameter_ranges, n_samples=10000
        )
        
        # 2. 训练神经网络
        self.neural_network = self.train_neural_network(
            parameter_samples, targets
        )
        
        # 3. 寻找最优参数
        optimal_parameters = self.find_optimal_parameters(targets)
        
        # 4. 计算置信区间
        confidence_intervals = self.calculate_confidence_intervals(
            optimal_parameters
        )
        
        return CalibrationResults(
            optimal_parameters=optimal_parameters,
            confidence_intervals=confidence_intervals,
            neural_network=self.neural_network
        )
```
