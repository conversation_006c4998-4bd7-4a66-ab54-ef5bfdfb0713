# 技术栈

## 核心技术栈表

| 类别 | 技术 | 版本 | 目的 | 选择理由 |
|------|------|------|------|----------|
| **后端语言** | Python | 3.9+ | 主要开发语言 | 丰富的科学计算生态系统，广泛的库支持 |
| **桌面框架** | PyQt6 | 6.5+ | 跨平台桌面应用 | 原生性能，丰富的UI组件，专业界面支持 |
| **数值计算** | NumPy | 1.24+ | 高性能数值计算 | 科学计算标准，C语言级性能 |
| **数据处理** | Pandas | 2.0+ | 数据操作和分析 | 强大的数据结构，Excel/CSV集成 |
| **机器学习** | TensorFlow | 2.13+ | 深度神经网络校准 | 成熟的深度学习框架，GPU加速支持 |
| **科学计算** | SciPy | 1.10+ | 统计函数和优化 | 专业的科学计算库，统计分析工具 |
| **数据可视化** | Matplotlib | 3.7+ | 科学图表绘制 | 出版级图表质量，高度可定制 |
| **交互式图表** | Plotly | 5.15+ | 交互式数据可视化 | 现代化交互体验，Web技术集成 |
| **数据库** | SQLite | 3.40+ | 本地数据存储 | 零配置，嵌入式，事务支持 |
| **ORM** | SQLAlchemy | 2.0+ | 数据库抽象层 | 强大的ORM，查询优化，迁移支持 |
| **测试框架** | pytest | 7.4+ | 单元和集成测试 | 简洁的测试语法，丰富的插件生态 |
| **代码质量** | Black | 23.0+ | 代码格式化 | 一致的代码风格，自动格式化 |
| **类型检查** | mypy | 1.5+ | 静态类型检查 | 提高代码质量，IDE支持 |
| **打包工具** | PyInstaller | 5.13+ | 可执行文件打包 | 跨平台打包，依赖自动检测 |
| **并行计算** | multiprocessing | 内置 | 多进程并行计算 | 充分利用多核CPU，绕过GIL限制 |
| **配置管理** | PyYAML | 6.0+ | 配置文件处理 | 人类可读的配置格式 |

## 开发工具栈

| 类别 | 工具 | 版本 | 目的 |
|------|------|------|------|
| **版本控制** | Git | 2.40+ | 代码版本管理 |
| **CI/CD** | GitHub Actions | - | 自动化测试和构建 |
| **容器化** | Docker | 24.0+ | 开发环境一致性 |
| **依赖管理** | Poetry | 1.5+ | Python依赖管理 |
| **文档生成** | Sphinx | 7.0+ | API文档自动生成 |
| **性能分析** | cProfile | 内置 | 性能瓶颈分析 |
