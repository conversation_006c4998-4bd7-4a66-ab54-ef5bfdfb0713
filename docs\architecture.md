# 结直肠癌筛查微观模拟模型 全栈架构文档

## 介绍

本文档定义了结直肠癌筛查微观模拟模型的完整全栈架构，包括后端系统、前端实现及其集成。它作为AI驱动开发的单一真实来源，确保整个技术栈的一致性。

### 项目概述

**项目名称**: 结直肠癌筛查微观模拟模型  
**架构类型**: 模块化桌面应用  
**部署模式**: 单机版跨平台桌面应用  
**目标用户**: 医疗政策研究人员、公共卫生官员、流行病学专家

### 架构目标

- **科学严谨性**: 支持复杂的微观模拟和机器学习校准
- **高性能计算**: 处理100万个体、100年周期的大规模模拟
- **模块化设计**: 支持新筛查工具和风险因素的灵活扩展
- **用户体验**: 提供专业级的科学研究界面
- **跨平台兼容**: 支持Windows、macOS、Linux操作系统

### 关键技术决策

1. **单体仓库架构**: 便于模拟引擎、校准模块和桌面界面的集成开发
2. **Python生态系统**: 利用丰富的科学计算和机器学习库
3. **桌面应用框架**: 提供原生性能和系统集成能力
4. **SQLite数据库**: 本地数据存储，无需外部数据库依赖
5. **模块化插件系统**: 支持疾病模块、筛查工具的动态扩展

## 技术栈

### 核心技术栈表

| 类别 | 技术 | 版本 | 目的 | 选择理由 |
|------|------|------|------|----------|
| **后端语言** | Python | 3.9+ | 主要开发语言 | 丰富的科学计算生态系统，广泛的库支持 |
| **桌面框架** | PyQt6 | 6.5+ | 跨平台桌面应用 | 原生性能，丰富的UI组件，专业界面支持 |
| **数值计算** | NumPy | 1.24+ | 高性能数值计算 | 科学计算标准，C语言级性能 |
| **数据处理** | Pandas | 2.0+ | 数据操作和分析 | 强大的数据结构，Excel/CSV集成 |
| **机器学习** | TensorFlow | 2.13+ | 深度神经网络校准 | 成熟的深度学习框架，GPU加速支持 |
| **科学计算** | SciPy | 1.10+ | 统计函数和优化 | 专业的科学计算库，统计分析工具 |
| **数据可视化** | Matplotlib | 3.7+ | 科学图表绘制 | 出版级图表质量，高度可定制 |
| **交互式图表** | Plotly | 5.15+ | 交互式数据可视化 | 现代化交互体验，Web技术集成 |
| **数据库** | SQLite | 3.40+ | 本地数据存储 | 零配置，嵌入式，事务支持 |
| **ORM** | SQLAlchemy | 2.0+ | 数据库抽象层 | 强大的ORM，查询优化，迁移支持 |
| **测试框架** | pytest | 7.4+ | 单元和集成测试 | 简洁的测试语法，丰富的插件生态 |
| **代码质量** | Black | 23.0+ | 代码格式化 | 一致的代码风格，自动格式化 |
| **类型检查** | mypy | 1.5+ | 静态类型检查 | 提高代码质量，IDE支持 |
| **打包工具** | PyInstaller | 5.13+ | 可执行文件打包 | 跨平台打包，依赖自动检测 |
| **并行计算** | multiprocessing | 内置 | 多进程并行计算 | 充分利用多核CPU，绕过GIL限制 |
| **配置管理** | PyYAML | 6.0+ | 配置文件处理 | 人类可读的配置格式 |

### 开发工具栈

| 类别 | 工具 | 版本 | 目的 |
|------|------|------|------|
| **版本控制** | Git | 2.40+ | 代码版本管理 |
| **CI/CD** | GitHub Actions | - | 自动化测试和构建 |
| **容器化** | Docker | 24.0+ | 开发环境一致性 |
| **依赖管理** | Poetry | 1.5+ | Python依赖管理 |
| **文档生成** | Sphinx | 7.0+ | API文档自动生成 |
| **性能分析** | cProfile | 内置 | 性能瓶颈分析 |

## 数据模型

### 疾病进展通路图

```mermaid
graph TD
    subgraph "腺瘤-癌变通路 (85%病例)"
        A1[正常] --> A2[低风险腺瘤]
        A2 --> A3[高风险腺瘤]
        A3 --> A4[临床前癌症]
        A4 --> A5[临床癌症 I期]
        A5 --> A6[临床癌症 II期]
        A6 --> A7[临床癌症 III期]
        A7 --> A8[临床癌症 IV期]
    end

    subgraph "锯齿状腺瘤通路 (15%病例)"
        S1[正常] --> S2[小锯齿状腺瘤]
        S2 --> S3[大锯齿状腺瘤]
        S3 --> S4[临床前癌症]
        S4 --> S5[临床癌症 I期]
        S5 --> S6[临床癌症 II期]
        S6 --> S7[临床癌症 III期]
        S7 --> S8[临床癌症 IV期]
    end

    subgraph "通路分配"
        N[新个体] --> |85%| A1
        N --> |15%| S1
    end

    style A1 fill:#e1f5fe
    style S1 fill:#e1f5fe
    style A8 fill:#ffebee
    style S8 fill:#ffebee
```

### 核心实体关系

```mermaid
erDiagram
    Individual ||--o{ HealthState : has
    Individual ||--o{ RiskFactor : has
    Individual ||--o{ ScreeningEvent : undergoes
    Individual }|--|| Population : belongs_to

    Population ||--o{ SimulationRun : simulated_in
    SimulationRun ||--o{ ScreeningStrategy : uses
    SimulationRun ||--o{ EconomicResult : produces

    ScreeningStrategy ||--o{ ScreeningTool : includes
    ScreeningTool ||--o{ PerformanceParameter : has

    DiseaseModel ||--o{ DiseaseState : defines
    DiseaseModel ||--o{ TransitionProbability : contains

    CalibrationRun ||--o{ ParameterSet : generates
    CalibrationRun ||--o{ CalibrationTarget : aims_for

    HealthState {
        string state_type "normal, low_risk_adenoma, high_risk_adenoma, small_serrated, large_serrated, preclinical_cancer, clinical_cancer"
        string cancer_stage "stage_1, stage_2, stage_3, stage_4"
        string pathway_type "adenoma_carcinoma, serrated_adenoma"
    }
```

### 数据模型详细定义

#### Individual（个体）
```python
@dataclass
class Individual:
    id: UUID
    age: int
    gender: Gender
    birth_year: int
    death_year: Optional[int]
    risk_factors: Dict[RiskFactorType, float]
    health_history: List[HealthState]
    screening_history: List[ScreeningEvent]
    current_disease_state: DiseaseState
    pathway_type: PathwayType  # 腺瘤-癌变 vs 锯齿状腺瘤
    anatomical_location: AnatomicalLocation
    cancer_stage: Optional[CancerStage]  # 癌症分期（I-IV期）
    created_at: datetime
    updated_at: datetime

# 疾病状态枚举
class DiseaseState(Enum):
    # 通用状态
    NORMAL = "normal"

    # 腺瘤-癌变通路状态
    LOW_RISK_ADENOMA = "low_risk_adenoma"
    HIGH_RISK_ADENOMA = "high_risk_adenoma"

    # 锯齿状腺瘤通路状态
    SMALL_SERRATED = "small_serrated"
    LARGE_SERRATED = "large_serrated"

    # 共同的癌症状态
    PRECLINICAL_CANCER = "preclinical_cancer"
    CLINICAL_CANCER = "clinical_cancer"

# 癌症分期枚举
class CancerStage(Enum):
    STAGE_I = "stage_1"
    STAGE_II = "stage_2"
    STAGE_III = "stage_3"
    STAGE_IV = "stage_4"

# 疾病通路类型
class PathwayType(Enum):
    ADENOMA_CARCINOMA = "adenoma_carcinoma"  # 85%病例
    SERRATED_ADENOMA = "serrated_adenoma"    # 15%病例

# Individual类的扩展方法
class Individual:
    # ... 其他属性和方法 ...

    def transition_to_state(self, new_state: DiseaseState,
                           transition_time: int,
                           new_stage: Optional[CancerStage] = None) -> None:
        """转换到新的疾病状态"""

        # 记录当前状态的结束时间
        if self.health_history:
            current_health_state = self.health_history[-1]
            current_health_state.end_time = transition_time

        # 创建新的健康状态记录
        new_health_state = HealthState(
            individual_id=self.id,
            state_type=new_state,
            cancer_stage=new_stage,
            start_time=transition_time,
            pathway_type=self.pathway_type,
            anatomical_location=self.anatomical_location
        )

        # 更新个体状态
        self.current_disease_state = new_state
        self.cancer_stage = new_stage
        self.health_history.append(new_health_state)
        self.updated_at = datetime.now()

    def get_state_entry_time(self, state: DiseaseState) -> Optional[int]:
        """获取进入指定状态的时间"""
        for health_state in reversed(self.health_history):
            if health_state.state_type == state:
                return health_state.start_time
        return None

    def get_time_in_current_state(self, current_time: int) -> int:
        """获取在当前状态的停留时间"""
        entry_time = self.get_state_entry_time(self.current_disease_state)
        if entry_time is not None:
            return current_time - entry_time
        return 0

    def is_in_adenoma_pathway(self) -> bool:
        """判断是否在腺瘤-癌变通路"""
        return self.pathway_type == PathwayType.ADENOMA_CARCINOMA

    def is_in_serrated_pathway(self) -> bool:
        """判断是否在锯齿状腺瘤通路"""
        return self.pathway_type == PathwayType.SERRATED_ADENOMA

    def has_adenoma(self) -> bool:
        """判断是否有腺瘤"""
        adenoma_states = {
            DiseaseState.LOW_RISK_ADENOMA,
            DiseaseState.HIGH_RISK_ADENOMA,
            DiseaseState.SMALL_SERRATED,
            DiseaseState.LARGE_SERRATED
        }
        return self.current_disease_state in adenoma_states

    def has_cancer(self) -> bool:
        """判断是否有癌症"""
        cancer_states = {
            DiseaseState.PRECLINICAL_CANCER,
            DiseaseState.CLINICAL_CANCER
        }
        return self.current_disease_state in cancer_states

    def get_cancer_stage_description(self) -> str:
        """获取癌症分期描述"""
        if not self.has_cancer():
            return "无癌症"

        if self.current_disease_state == DiseaseState.PRECLINICAL_CANCER:
            return "临床前癌症"

        if self.cancer_stage:
            stage_descriptions = {
                CancerStage.STAGE_I: "I期癌症",
                CancerStage.STAGE_II: "II期癌症",
                CancerStage.STAGE_III: "III期癌症",
                CancerStage.STAGE_IV: "IV期癌症"
            }
            return stage_descriptions.get(self.cancer_stage, "未知分期癌症")

        return "临床癌症（未分期）"

# 健康状态数据类
@dataclass
class HealthState:
    individual_id: UUID
    state_type: DiseaseState
    cancer_stage: Optional[CancerStage]
    start_time: int  # 模拟时间（月）
    end_time: Optional[int] = None
    pathway_type: PathwayType = None
    anatomical_location: Optional[str] = None
    transition_probability: Optional[float] = None
    created_at: datetime = field(default_factory=datetime.now)
```

#### Population（人群）
```python
@dataclass
class Population:
    id: UUID
    name: str
    size: int
    age_distribution: Dict[int, float]
    gender_ratio: float  # 男性比例
    life_table: LifeTable
    risk_factor_distribution: Dict[RiskFactorType, Distribution]
    individuals: List[Individual]
    created_at: datetime
```

#### ScreeningStrategy（筛查策略）
```python
@dataclass
class ScreeningStrategy:
    id: UUID
    name: str
    description: str
    start_age: int
    end_age: int
    tools: List[ScreeningTool]
    intervals: Dict[ScreeningTool, int]  # 筛查间隔（月）
    compliance_rates: Dict[ScreeningTool, float]
    follow_up_compliance: float  # 阳性后肠镜依从性
    sequential_implementation: bool
    created_at: datetime
```

#### EconomicParameters（经济参数）
```python
@dataclass
class EconomicParameters:
    id: UUID
    screening_costs: Dict[ScreeningTool, float]
    treatment_costs: Dict[CancerStage, float]
    indirect_costs: Dict[str, float]
    discount_rate: float = 0.03  # 3%年度折现率
    qaly_weights: Dict[HealthState, float]
    currency: str = "CNY"
    reference_year: int
    created_at: datetime
```

## API 规格

### 核心API端点设计

#### 模拟管理API
```python
# 创建新模拟
POST /api/simulations
{
    "name": "筛查策略比较研究",
    "population_config": {...},
    "disease_model_config": {...},
    "screening_strategies": [...],
    "economic_parameters": {...},
    "simulation_parameters": {
        "duration_years": 100,
        "time_step_months": 3
    }
}

# 启动模拟执行
POST /api/simulations/{id}/run
{
    "parallel_processes": 4,
    "memory_limit_gb": 8,
    "checkpoint_interval": 1000
}

# 获取模拟状态
GET /api/simulations/{id}/status
{
    "status": "running",
    "progress_percent": 45.2,
    "current_year": 45,
    "estimated_completion": "2025-02-01T15:30:00Z",
    "memory_usage_gb": 6.2,
    "cpu_usage_percent": 85.0
}
```

#### 数据管理API
```python
# 导入生命表数据
POST /api/data/life-tables
Content-Type: multipart/form-data
{
    "file": <CSV文件>,
    "region": "中国",
    "year": 2020,
    "source": "国家统计局"
}

# 导入校准基准值
POST /api/data/calibration-targets
{
    "adenoma_prevalence": {
        "age_50_54": {"male": 0.15, "female": 0.12},
        "age_55_59": {"male": 0.18, "female": 0.14}
    },
    "cancer_incidence": {...},
    "source": "中国癌症统计2020"
}
```

#### 校准API
```python
# 启动机器学习校准
POST /api/calibration/start
{
    "parameter_ranges": {
        "adenoma_initiation_rate": [0.001, 0.01],
        "progression_multiplier": [0.8, 1.2]
    },
    "sampling_method": "latin_hypercube",
    "sample_size": 10000,
    "neural_network_config": {
        "hidden_layers": [128, 64, 32],
        "activation": "relu",
        "learning_rate": 0.001
    }
}

# 获取校准进度
GET /api/calibration/{id}/progress
{
    "status": "training",
    "epoch": 150,
    "total_epochs": 500,
    "training_loss": 0.0234,
    "validation_loss": 0.0267,
    "convergence_status": "improving"
}
```

#### 结果分析API
```python
# 获取模拟结果摘要
GET /api/results/{simulation_id}/summary
{
    "population_outcomes": {
        "total_individuals": 1000000,
        "cancer_cases_prevented": 15420,
        "deaths_prevented": 8930,
        "life_years_gained": 89300
    },
    "economic_outcomes": {
        "total_screening_costs": 45000000,
        "total_treatment_costs": 120000000,
        "qalys_gained": 67200,
        "icer": 2455.30
    }
}

# 导出详细结果
POST /api/results/{simulation_id}/export
{
    "format": "csv",
    "include_individual_data": false,
    "aggregation_level": "yearly",
    "metrics": ["incidence", "mortality", "costs", "qalys"]
}
```

## 数据库架构

### SQLite数据库设计

#### 核心表结构

```sql
-- 个体表
CREATE TABLE individuals (
    id TEXT PRIMARY KEY,
    population_id TEXT NOT NULL,
    age INTEGER NOT NULL,
    gender TEXT NOT NULL,
    birth_year INTEGER NOT NULL,
    death_year INTEGER,
    pathway_type TEXT NOT NULL,
    anatomical_location TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (population_id) REFERENCES populations(id)
);

-- 健康状态历史表
CREATE TABLE health_states (
    id TEXT PRIMARY KEY,
    individual_id TEXT NOT NULL,
    state_type TEXT NOT NULL,  -- normal, low_risk_adenoma, high_risk_adenoma, small_serrated, large_serrated, preclinical_cancer, clinical_cancer
    cancer_stage TEXT,         -- stage_1, stage_2, stage_3, stage_4 (仅当state_type为clinical_cancer时)
    start_time INTEGER NOT NULL,  -- 模拟时间（月）
    end_time INTEGER,
    transition_probability REAL,
    pathway_type TEXT NOT NULL,   -- adenoma_carcinoma, serrated_adenoma
    anatomical_location TEXT,    -- proximal_colon, distal_colon, rectum
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (individual_id) REFERENCES individuals(id),
    CHECK (state_type IN ('normal', 'low_risk_adenoma', 'high_risk_adenoma', 'small_serrated', 'large_serrated', 'preclinical_cancer', 'clinical_cancer')),
    CHECK (cancer_stage IS NULL OR cancer_stage IN ('stage_1', 'stage_2', 'stage_3', 'stage_4')),
    CHECK (pathway_type IN ('adenoma_carcinoma', 'serrated_adenoma'))
);

-- 筛查事件表
CREATE TABLE screening_events (
    id TEXT PRIMARY KEY,
    individual_id TEXT NOT NULL,
    tool_type TEXT NOT NULL,
    event_time INTEGER NOT NULL,  -- 模拟时间（月）
    result TEXT NOT NULL,  -- positive/negative/inadequate
    true_state TEXT NOT NULL,
    follow_up_completed BOOLEAN DEFAULT FALSE,
    costs REAL NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (individual_id) REFERENCES individuals(id)
);

-- 模拟运行表
CREATE TABLE simulation_runs (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    status TEXT NOT NULL,  -- pending/running/completed/failed
    population_id TEXT NOT NULL,
    strategy_id TEXT NOT NULL,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    progress_percent REAL DEFAULT 0,
    current_year INTEGER DEFAULT 0,
    memory_usage_gb REAL,
    cpu_usage_percent REAL,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (population_id) REFERENCES populations(id),
    FOREIGN KEY (strategy_id) REFERENCES screening_strategies(id)
);

-- 经济结果表
CREATE TABLE economic_results (
    id TEXT PRIMARY KEY,
    simulation_id TEXT NOT NULL,
    year INTEGER NOT NULL,
    screening_costs REAL NOT NULL,
    treatment_costs REAL NOT NULL,
    indirect_costs REAL NOT NULL,
    qalys_gained REAL NOT NULL,
    life_years_gained REAL NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (simulation_id) REFERENCES simulation_runs(id)
);
```

#### 索引优化

```sql
-- 性能关键索引
CREATE INDEX idx_individuals_population ON individuals(population_id);
CREATE INDEX idx_health_states_individual ON health_states(individual_id);
CREATE INDEX idx_health_states_time ON health_states(start_time, end_time);
CREATE INDEX idx_screening_events_individual ON screening_events(individual_id);
CREATE INDEX idx_screening_events_time ON screening_events(event_time);
CREATE INDEX idx_economic_results_simulation ON economic_results(simulation_id);
CREATE INDEX idx_economic_results_year ON economic_results(year);

-- 复合索引用于复杂查询
CREATE INDEX idx_individuals_age_gender ON individuals(age, gender);
CREATE INDEX idx_screening_events_tool_result ON screening_events(tool_type, result);
```

### 数据分区策略

```python
# 大规模数据分区管理
class DatabasePartitionManager:
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.partition_size = 100000  # 每个分区10万个体
    
    def create_partition_tables(self, simulation_id: str, population_size: int):
        """为大规模模拟创建分区表"""
        num_partitions = math.ceil(population_size / self.partition_size)
        
        for i in range(num_partitions):
            partition_name = f"individuals_{simulation_id}_{i}"
            self.create_individual_partition(partition_name)
    
    def create_individual_partition(self, table_name: str):
        """创建个体数据分区表"""
        sql = f"""
        CREATE TABLE {table_name} (
            LIKE individuals INCLUDING ALL
        );
        """
        # 执行SQL创建分区表

## 前端架构

### PyQt6桌面应用架构

#### 应用程序结构

```
src/interfaces/desktop/
├── main.py                 # 应用程序入口点
├── app/
│   ├── __init__.py
│   ├── application.py      # 主应用程序类
│   ├── window_manager.py   # 窗口管理器
│   └── event_bus.py        # 事件总线
├── windows/
│   ├── __init__.py
│   ├── main_window.py      # 主窗口
│   ├── config_wizard.py    # 配置向导
│   ├── simulation_monitor.py # 模拟监控窗口
│   ├── results_analyzer.py # 结果分析窗口
│   └── data_manager.py     # 数据管理窗口
├── widgets/
│   ├── __init__.py
│   ├── parameter_panel.py  # 参数配置面板
│   ├── chart_widget.py     # 图表组件
│   ├── data_table.py       # 数据表格
│   ├── progress_monitor.py # 进度监控
│   └── file_uploader.py    # 文件上传组件
├── models/
│   ├── __init__.py
│   ├── ui_models.py        # UI数据模型
│   └── view_models.py      # 视图模型
├── services/
│   ├── __init__.py
│   ├── simulation_service.py # 模拟服务
│   ├── data_service.py     # 数据服务
│   └── export_service.py   # 导出服务
├── resources/
│   ├── icons/              # 图标资源
│   ├── styles/             # 样式表
│   └── translations/       # 国际化文件
└── utils/
    ├── __init__.py
    ├── validators.py       # 数据验证
    ├── formatters.py       # 数据格式化
    └── helpers.py          # 辅助函数
```

#### 主应用程序类

```python
class ColorectalScreeningApp(QApplication):
    """主应用程序类"""

    def __init__(self, sys_argv):
        super().__init__(sys_argv)
        self.setApplicationName("结直肠癌筛查微观模拟模型")
        self.setApplicationVersion("1.0.0")
        self.setOrganizationName("深圳市南山区慢性病防治院")

        # 初始化核心组件
        self.window_manager = WindowManager()
        self.event_bus = EventBus()
        self.simulation_service = SimulationService()
        self.data_service = DataService()

        # 设置应用程序样式
        self.setup_styles()

        # 创建主窗口
        self.main_window = MainWindow()
        self.main_window.show()

    def setup_styles(self):
        """设置应用程序样式"""
        style_path = Path(__file__).parent / "resources" / "styles" / "main.qss"
        with open(style_path, 'r', encoding='utf-8') as f:
            self.setStyleSheet(f.read())
```

#### 主窗口设计

```python
class MainWindow(QMainWindow):
    """主窗口类"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("结直肠癌筛查微观模拟模型")
        self.setMinimumSize(1200, 800)

        # 创建中央部件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        # 创建布局
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_tool_bar()
        self.setup_status_bar()

        # 连接信号槽
        self.connect_signals()

    def setup_ui(self):
        """设置用户界面"""
        layout = QHBoxLayout(self.central_widget)

        # 左侧导航面板
        self.nav_panel = NavigationPanel()
        layout.addWidget(self.nav_panel, 0)

        # 中央内容区域
        self.content_stack = QStackedWidget()
        layout.addWidget(self.content_stack, 1)

        # 添加各个页面
        self.dashboard_page = DashboardPage()
        self.config_page = ConfigurationPage()
        self.results_page = ResultsPage()
        self.data_page = DataManagementPage()

        self.content_stack.addWidget(self.dashboard_page)
        self.content_stack.addWidget(self.config_page)
        self.content_stack.addWidget(self.results_page)
        self.content_stack.addWidget(self.data_page)
```

### 组件架构

#### 参数配置组件

```python
class ParameterConfigPanel(QWidget):
    """参数配置面板基类"""

    parameter_changed = pyqtSignal(str, object)  # 参数变化信号
    validation_error = pyqtSignal(str, str)      # 验证错误信号

    def __init__(self, parameter_schema: Dict):
        super().__init__()
        self.schema = parameter_schema
        self.validators = {}
        self.current_values = {}

        self.setup_ui()
        self.setup_validation()

    def setup_ui(self):
        """根据schema动态创建UI"""
        layout = QVBoxLayout(self)

        for param_name, param_config in self.schema.items():
            param_widget = self.create_parameter_widget(param_name, param_config)
            layout.addWidget(param_widget)

    def create_parameter_widget(self, name: str, config: Dict) -> QWidget:
        """根据参数类型创建对应的输入组件"""
        param_type = config.get('type', 'float')

        if param_type == 'float':
            return self.create_float_input(name, config)
        elif param_type == 'int':
            return self.create_int_input(name, config)
        elif param_type == 'choice':
            return self.create_choice_input(name, config)
        elif param_type == 'file':
            return self.create_file_input(name, config)
        else:
            return self.create_text_input(name, config)
```

#### 数据可视化组件

```python
class ScientificChartWidget(QWidget):
    """科学图表组件"""

    def __init__(self, chart_type: str = 'line'):
        super().__init__()
        self.chart_type = chart_type
        self.figure = plt.figure(figsize=(10, 6))
        self.canvas = FigureCanvas(self.figure)
        self.toolbar = NavigationToolbar(self.canvas, self)

        self.setup_ui()
        self.setup_style()

    def setup_ui(self):
        """设置图表UI"""
        layout = QVBoxLayout(self)
        layout.addWidget(self.toolbar)
        layout.addWidget(self.canvas)

    def setup_style(self):
        """设置科学图表样式"""
        plt.style.use('seaborn-v0_8-whitegrid')
        self.figure.patch.set_facecolor('white')

    def plot_simulation_results(self, data: pd.DataFrame,
                               x_column: str, y_column: str,
                               group_column: str = None):
        """绘制模拟结果"""
        self.figure.clear()
        ax = self.figure.add_subplot(111)

        if group_column:
            for group in data[group_column].unique():
                group_data = data[data[group_column] == group]
                ax.plot(group_data[x_column], group_data[y_column],
                       label=group, linewidth=2)
            ax.legend()
        else:
            ax.plot(data[x_column], data[y_column], linewidth=2)

        ax.set_xlabel(x_column)
        ax.set_ylabel(y_column)
        ax.grid(True, alpha=0.3)

        self.canvas.draw()
```

#### 进度监控组件

```python
class SimulationProgressWidget(QWidget):
    """模拟进度监控组件"""

    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_progress)

    def setup_ui(self):
        """设置进度监控UI"""
        layout = QVBoxLayout(self)

        # 总体进度
        self.overall_progress = QProgressBar()
        self.overall_progress.setTextVisible(True)
        layout.addWidget(QLabel("总体进度:"))
        layout.addWidget(self.overall_progress)

        # 当前年份进度
        self.year_progress = QProgressBar()
        layout.addWidget(QLabel("当前年份:"))
        layout.addWidget(self.year_progress)

        # 性能指标
        self.performance_group = QGroupBox("性能监控")
        perf_layout = QGridLayout(self.performance_group)

        self.memory_label = QLabel("内存使用: 0 GB")
        self.cpu_label = QLabel("CPU使用: 0%")
        self.eta_label = QLabel("预计完成: --")

        perf_layout.addWidget(self.memory_label, 0, 0)
        perf_layout.addWidget(self.cpu_label, 0, 1)
        perf_layout.addWidget(self.eta_label, 1, 0, 1, 2)

        layout.addWidget(self.performance_group)

    def start_monitoring(self, simulation_id: str):
        """开始监控指定模拟"""
        self.simulation_id = simulation_id
        self.timer.start(1000)  # 每秒更新一次

    def update_progress(self):
        """更新进度信息"""
        try:
            status = self.get_simulation_status(self.simulation_id)

            self.overall_progress.setValue(int(status['progress_percent']))
            self.year_progress.setValue(int(status['current_year']))

            self.memory_label.setText(f"内存使用: {status['memory_usage_gb']:.1f} GB")
            self.cpu_label.setText(f"CPU使用: {status['cpu_usage_percent']:.1f}%")

            if status['estimated_completion']:
                eta = datetime.fromisoformat(status['estimated_completion'])
                self.eta_label.setText(f"预计完成: {eta.strftime('%H:%M:%S')}")

        except Exception as e:
            print(f"更新进度失败: {e}")

## 后端架构

### 核心模拟引擎

#### 模拟引擎架构

```python
class MicrosimulationEngine:
    """微观模拟引擎核心类"""

    def __init__(self, config: SimulationConfig):
        self.config = config
        self.population = None
        self.disease_model = DiseaseModel(config.disease_config)
        self.screening_model = ScreeningModel(config.screening_config)
        self.economic_model = EconomicModel(config.economic_config)

        # 性能监控
        self.performance_monitor = PerformanceMonitor()
        self.memory_manager = MemoryManager(max_memory_gb=8)

        # 并行计算支持
        self.process_pool = None
        self.parallel_enabled = config.parallel_config.enabled

    def initialize_population(self, population_config: PopulationConfig):
        """初始化人群队列"""
        self.population = Population.create_from_config(population_config)

        # 分配风险因素
        self.assign_risk_factors()

        # 初始化疾病状态
        self.initialize_disease_states()

        # 内存优化：大人群分批处理
        if self.population.size > 500000:
            self.enable_batch_processing()

    def run_simulation(self, duration_years: int = 100) -> SimulationResults:
        """运行完整模拟"""
        results = SimulationResults()

        try:
            # 启动性能监控
            self.performance_monitor.start()

            # 主模拟循环
            for year in range(duration_years):
                for quarter in range(4):
                    current_time = year * 12 + quarter * 3

                    # 疾病进展
                    self.advance_disease_states(current_time)

                    # 筛查干预
                    self.apply_screening_interventions(current_time)

                    # 自然死亡
                    self.apply_natural_mortality(current_time)

                    # 经济计算
                    self.calculate_economic_outcomes(current_time)

                    # 收集结果
                    results.add_timepoint_data(current_time, self.collect_outcomes())

                    # 内存管理
                    if current_time % 12 == 0:  # 每年检查一次
                        self.memory_manager.cleanup_if_needed()

                    # 进度报告
                    progress = (year * 4 + quarter + 1) / (duration_years * 4) * 100
                    self.report_progress(progress, year, quarter)

            return results

        except Exception as e:
            self.handle_simulation_error(e)
            raise
        finally:
            self.performance_monitor.stop()
            self.cleanup_resources()
```

#### 疾病建模模块

```python
class DiseaseModel:
    """疾病自然史建模

    支持两种疾病进展通路：
    1. 腺瘤-癌变通路（85%病例）：正常→低风险腺瘤→高风险腺瘤→临床前癌症→临床癌症（I-IV期）
    2. 锯齿状腺瘤通路（15%病例）：正常→小锯齿状→大锯齿状→临床前癌症→临床癌症（I-IV期）
    """

    def __init__(self, config: DiseaseConfig):
        self.config = config

        # 双通路疾病模型
        self.adenoma_pathway = AdenomaCarcinomaPathway(config.adenoma_config)
        self.serrated_pathway = SerratedAdenomaPathway(config.serrated_config)

        # 风险因素影响
        self.risk_factor_effects = RiskFactorModel(config.risk_factors)

        # 通路分配概率
        self.adenoma_pathway_probability = 0.85  # 85%腺瘤-癌变通路
        self.serrated_pathway_probability = 0.15  # 15%锯齿状腺瘤通路

    def assign_pathway_to_individual(self, individual: Individual) -> None:
        """为个体分配疾病通路"""
        if random.random() < self.adenoma_pathway_probability:
            individual.pathway_type = PathwayType.ADENOMA_CARCINOMA
        else:
            individual.pathway_type = PathwayType.SERRATED_ADENOMA

    def advance_individual_disease(self, individual: Individual,
                                 current_time: int) -> None:
        """推进个体疾病状态"""

        # 选择疾病通路
        if individual.pathway_type == PathwayType.ADENOMA_CARCINOMA:
            pathway = self.adenoma_pathway
        else:
            pathway = self.serrated_pathway

        # 计算转换概率
        transition_prob = pathway.calculate_transition_probability(
            individual, current_time
        )

        # 应用风险因素影响
        adjusted_prob = self.risk_factor_effects.adjust_probability(
            transition_prob, individual.risk_factors
        )

        # 执行状态转换
        if random.random() < adjusted_prob:
            new_state, new_stage = pathway.get_next_state(
                individual.current_disease_state,
                individual.cancer_stage
            )
            individual.transition_to_state(new_state, current_time, new_stage)

    def validate_disease_state_transition(self, individual: Individual,
                                        new_state: DiseaseState,
                                        new_stage: Optional[CancerStage]) -> bool:
        """验证疾病状态转换的合法性"""
        current_state = individual.current_disease_state
        current_stage = individual.cancer_stage
        pathway_type = individual.pathway_type

        # 腺瘤-癌变通路验证
        if pathway_type == PathwayType.ADENOMA_CARCINOMA:
            valid_transitions = {
                DiseaseState.NORMAL: [DiseaseState.LOW_RISK_ADENOMA],
                DiseaseState.LOW_RISK_ADENOMA: [DiseaseState.HIGH_RISK_ADENOMA],
                DiseaseState.HIGH_RISK_ADENOMA: [DiseaseState.PRECLINICAL_CANCER],
                DiseaseState.PRECLINICAL_CANCER: [DiseaseState.CLINICAL_CANCER],
                DiseaseState.CLINICAL_CANCER: [DiseaseState.CLINICAL_CANCER]  # 分期进展
            }

        # 锯齿状腺瘤通路验证
        else:
            valid_transitions = {
                DiseaseState.NORMAL: [DiseaseState.SMALL_SERRATED],
                DiseaseState.SMALL_SERRATED: [DiseaseState.LARGE_SERRATED],
                DiseaseState.LARGE_SERRATED: [DiseaseState.PRECLINICAL_CANCER],
                DiseaseState.PRECLINICAL_CANCER: [DiseaseState.CLINICAL_CANCER],
                DiseaseState.CLINICAL_CANCER: [DiseaseState.CLINICAL_CANCER]  # 分期进展
            }

        # 检查状态转换是否合法
        if new_state not in valid_transitions.get(current_state, []):
            return False

        # 检查癌症分期转换是否合法
        if new_state == DiseaseState.CLINICAL_CANCER and new_stage:
            if current_state != DiseaseState.CLINICAL_CANCER:
                # 从非癌症状态转换到癌症，必须是I期
                return new_stage == CancerStage.STAGE_I
            else:
                # 癌症分期进展验证
                stage_progression = {
                    CancerStage.STAGE_I: [CancerStage.STAGE_II],
                    CancerStage.STAGE_II: [CancerStage.STAGE_III],
                    CancerStage.STAGE_III: [CancerStage.STAGE_IV],
                    CancerStage.STAGE_IV: [CancerStage.STAGE_IV]  # IV期不再进展
                }
                return new_stage in stage_progression.get(current_stage, [])

        return True

class AdenomaCarcinomaPathway:
    """腺瘤-癌变通路（85%病例）

    进展序列：正常 → 低风险腺瘤 → 高风险腺瘤 → 临床前癌症 → 临床癌症（I-IV期）
    """

    def __init__(self, config: AdenomaConfig):
        self.config = config
        self.states = [
            DiseaseState.NORMAL,
            DiseaseState.LOW_RISK_ADENOMA,
            DiseaseState.HIGH_RISK_ADENOMA,
            DiseaseState.PRECLINICAL_CANCER,
            DiseaseState.CLINICAL_CANCER
        ]

        # 癌症分期进展序列
        self.cancer_stages = [
            CancerStage.STAGE_I,
            CancerStage.STAGE_II,
            CancerStage.STAGE_III,
            CancerStage.STAGE_IV
        ]

    def get_next_state(self, current_state: DiseaseState,
                      current_stage: Optional[CancerStage] = None) -> Tuple[DiseaseState, Optional[CancerStage]]:
        """获取下一个疾病状态"""
        if current_state == DiseaseState.NORMAL:
            return DiseaseState.LOW_RISK_ADENOMA, None
        elif current_state == DiseaseState.LOW_RISK_ADENOMA:
            return DiseaseState.HIGH_RISK_ADENOMA, None
        elif current_state == DiseaseState.HIGH_RISK_ADENOMA:
            return DiseaseState.PRECLINICAL_CANCER, None
        elif current_state == DiseaseState.PRECLINICAL_CANCER:
            return DiseaseState.CLINICAL_CANCER, CancerStage.STAGE_I
        elif current_state == DiseaseState.CLINICAL_CANCER:
            # 癌症分期进展
            if current_stage == CancerStage.STAGE_I:
                return DiseaseState.CLINICAL_CANCER, CancerStage.STAGE_II
            elif current_stage == CancerStage.STAGE_II:
                return DiseaseState.CLINICAL_CANCER, CancerStage.STAGE_III
            elif current_stage == CancerStage.STAGE_III:
                return DiseaseState.CLINICAL_CANCER, CancerStage.STAGE_IV
            else:
                # 已经是IV期，不再进展
                return current_state, current_stage
        else:
            return current_state, current_stage

class SerratedAdenomaPathway:
    """锯齿状腺瘤-癌变通路（15%病例）

    进展序列：正常 → 小锯齿状腺瘤 → 大锯齿状腺瘤 → 临床前癌症 → 临床癌症（I-IV期）
    """

    def __init__(self, config: SerratedConfig):
        self.config = config
        self.states = [
            DiseaseState.NORMAL,
            DiseaseState.SMALL_SERRATED,
            DiseaseState.LARGE_SERRATED,
            DiseaseState.PRECLINICAL_CANCER,
            DiseaseState.CLINICAL_CANCER
        ]

        # 癌症分期进展序列（与腺瘤通路相同）
        self.cancer_stages = [
            CancerStage.STAGE_I,
            CancerStage.STAGE_II,
            CancerStage.STAGE_III,
            CancerStage.STAGE_IV
        ]

    def get_next_state(self, current_state: DiseaseState,
                      current_stage: Optional[CancerStage] = None) -> Tuple[DiseaseState, Optional[CancerStage]]:
        """获取下一个疾病状态"""
        if current_state == DiseaseState.NORMAL:
            return DiseaseState.SMALL_SERRATED, None
        elif current_state == DiseaseState.SMALL_SERRATED:
            return DiseaseState.LARGE_SERRATED, None
        elif current_state == DiseaseState.LARGE_SERRATED:
            return DiseaseState.PRECLINICAL_CANCER, None
        elif current_state == DiseaseState.PRECLINICAL_CANCER:
            return DiseaseState.CLINICAL_CANCER, CancerStage.STAGE_I
        elif current_state == DiseaseState.CLINICAL_CANCER:
            # 癌症分期进展（与腺瘤通路相同逻辑）
            if current_stage == CancerStage.STAGE_I:
                return DiseaseState.CLINICAL_CANCER, CancerStage.STAGE_II
            elif current_stage == CancerStage.STAGE_II:
                return DiseaseState.CLINICAL_CANCER, CancerStage.STAGE_III
            elif current_stage == CancerStage.STAGE_III:
                return DiseaseState.CLINICAL_CANCER, CancerStage.STAGE_IV
            else:
                # 已经是IV期，不再进展
                return current_state, current_stage
        else:
            return current_state, current_stage

    def calculate_transition_probability(self, individual: Individual,
                                       time: int) -> float:
        """计算状态转换概率"""
        current_state = individual.current_disease_state
        age = individual.get_age_at_time(time)
        gender = individual.gender

        # 基础转换率
        base_rate = self.config.base_transition_rates[current_state]

        # 年龄调整（sigmoid函数）
        age_factor = self.calculate_age_factor(age, gender)

        # 性别倍数
        gender_multiplier = self.config.gender_multipliers[gender]

        # 停留时间影响
        dwell_time = time - individual.get_state_entry_time(current_state)
        dwell_factor = self.calculate_dwell_time_factor(dwell_time, current_state)

        return base_rate * age_factor * gender_multiplier * dwell_factor

    def calculate_age_factor(self, age: int, gender: Gender) -> float:
        """计算年龄因子（sigmoid函数）"""
        if gender == Gender.MALE:
            midpoint = self.config.male_age_midpoint
            steepness = self.config.male_age_steepness
        else:
            midpoint = self.config.female_age_midpoint
            steepness = self.config.female_age_steepness

        return 1 / (1 + math.exp(-steepness * (age - midpoint)))
```

#### 筛查模拟模块

```python
class ScreeningModel:
    """筛查策略模拟"""

    def __init__(self, config: ScreeningConfig):
        self.config = config
        self.tools = {
            tool.type: tool for tool in config.screening_tools
        }
        self.strategies = config.screening_strategies

    def apply_screening_to_individual(self, individual: Individual,
                                    strategy: ScreeningStrategy,
                                    current_time: int) -> Optional[ScreeningEvent]:
        """对个体应用筛查策略"""

        # 检查是否符合筛查条件
        if not self.is_eligible_for_screening(individual, strategy, current_time):
            return None

        # 检查依从性
        if not self.check_compliance(individual, strategy):
            return None

        # 确定筛查工具
        tool = self.select_screening_tool(individual, strategy, current_time)

        # 执行筛查
        result = self.perform_screening(individual, tool, current_time)

        # 记录筛查事件
        event = ScreeningEvent(
            individual_id=individual.id,
            tool_type=tool.type,
            event_time=current_time,
            result=result.outcome,
            true_state=individual.current_disease_state,
            costs=tool.cost
        )

        # 处理阳性结果
        if result.outcome == ScreeningOutcome.POSITIVE:
            self.handle_positive_result(individual, event, current_time)

        return event

    def perform_screening(self, individual: Individual,
                         tool: ScreeningTool,
                         time: int) -> ScreeningResult:
        """执行筛查检测"""
        true_state = individual.current_disease_state
        cancer_stage = individual.cancer_stage

        # 获取工具对当前疾病状态的敏感性和特异性
        sensitivity = tool.get_sensitivity(true_state, cancer_stage)
        specificity = tool.get_specificity(true_state)

        # 确定检测结果
        detectable_states = [
            DiseaseState.LOW_RISK_ADENOMA,
            DiseaseState.HIGH_RISK_ADENOMA,
            DiseaseState.SMALL_SERRATED,
            DiseaseState.LARGE_SERRATED,
            DiseaseState.PRECLINICAL_CANCER,
            DiseaseState.CLINICAL_CANCER
        ]

        if true_state in detectable_states:
            # 有病变
            if random.random() < sensitivity:
                outcome = ScreeningOutcome.POSITIVE
            else:
                outcome = ScreeningOutcome.NEGATIVE  # 假阴性
        else:
            # 无病变（正常状态）
            if random.random() < specificity:
                outcome = ScreeningOutcome.NEGATIVE
            else:
                outcome = ScreeningOutcome.POSITIVE  # 假阳性

        return ScreeningResult(
            outcome=outcome,
            sensitivity=sensitivity,
            specificity=specificity,
            detection_details=self.get_detection_details(individual, tool)
        )

# 筛查工具类扩展
class ScreeningTool:
    def __init__(self, tool_type: str, config: Dict):
        self.tool_type = tool_type
        self.config = config

        # 针对不同疾病状态的敏感性配置
        self.sensitivity_by_state = {
            DiseaseState.NORMAL: 0.0,  # 正常状态无法检出
            DiseaseState.LOW_RISK_ADENOMA: config.get('sensitivity_low_risk_adenoma', 0.2),
            DiseaseState.HIGH_RISK_ADENOMA: config.get('sensitivity_high_risk_adenoma', 0.6),
            DiseaseState.SMALL_SERRATED: config.get('sensitivity_small_serrated', 0.15),
            DiseaseState.LARGE_SERRATED: config.get('sensitivity_large_serrated', 0.5),
            DiseaseState.PRECLINICAL_CANCER: config.get('sensitivity_preclinical_cancer', 0.8),
            DiseaseState.CLINICAL_CANCER: config.get('sensitivity_clinical_cancer', 0.95)
        }

        # 针对不同癌症分期的敏感性调整
        self.sensitivity_by_cancer_stage = {
            CancerStage.STAGE_I: config.get('sensitivity_stage_1', 0.85),
            CancerStage.STAGE_II: config.get('sensitivity_stage_2', 0.92),
            CancerStage.STAGE_III: config.get('sensitivity_stage_3', 0.96),
            CancerStage.STAGE_IV: config.get('sensitivity_stage_4', 0.98)
        }

        # 特异性（对正常状态）
        self.specificity = config.get('specificity', 0.9)

        # 成本
        self.cost = config.get('cost', 0.0)

    def get_sensitivity(self, disease_state: DiseaseState,
                       cancer_stage: Optional[CancerStage] = None) -> float:
        """获取对指定疾病状态的敏感性"""
        base_sensitivity = self.sensitivity_by_state.get(disease_state, 0.0)

        # 如果是临床癌症，根据分期调整敏感性
        if disease_state == DiseaseState.CLINICAL_CANCER and cancer_stage:
            stage_sensitivity = self.sensitivity_by_cancer_stage.get(cancer_stage, base_sensitivity)
            return stage_sensitivity

        return base_sensitivity

    def get_specificity(self, disease_state: DiseaseState) -> float:
        """获取特异性"""
        return self.specificity

# 筛查工具配置示例
class FITScreeningTool(ScreeningTool):
    """粪便免疫化学检测（FIT）"""

    def __init__(self):
        config = {
            'sensitivity_low_risk_adenoma': 0.15,
            'sensitivity_high_risk_adenoma': 0.25,
            'sensitivity_small_serrated': 0.10,
            'sensitivity_large_serrated': 0.20,
            'sensitivity_preclinical_cancer': 0.70,
            'sensitivity_clinical_cancer': 0.85,
            'sensitivity_stage_1': 0.75,
            'sensitivity_stage_2': 0.85,
            'sensitivity_stage_3': 0.90,
            'sensitivity_stage_4': 0.95,
            'specificity': 0.95,
            'cost': 50.0  # 人民币
        }
        super().__init__('FIT', config)

class ColonoscopyScreeningTool(ScreeningTool):
    """结肠镜检查"""

    def __init__(self):
        config = {
            'sensitivity_low_risk_adenoma': 0.85,
            'sensitivity_high_risk_adenoma': 0.95,
            'sensitivity_small_serrated': 0.75,
            'sensitivity_large_serrated': 0.90,
            'sensitivity_preclinical_cancer': 0.98,
            'sensitivity_clinical_cancer': 0.99,
            'sensitivity_stage_1': 0.98,
            'sensitivity_stage_2': 0.99,
            'sensitivity_stage_3': 0.99,
            'sensitivity_stage_4': 0.99,
            'specificity': 0.98,
            'cost': 800.0  # 人民币
        }
        super().__init__('Colonoscopy', config)
```

#### 机器学习校准模块

```python
class MLCalibrationEngine:
    """机器学习校准引擎"""

    def __init__(self, config: CalibrationConfig):
        self.config = config
        self.parameter_sampler = LatinHypercubeSampler(config.sampling_config)
        self.neural_network = None
        self.calibration_targets = config.targets

    def run_calibration(self) -> CalibrationResults:
        """执行完整校准流程"""

        # 1. 参数抽样
        parameter_sets = self.parameter_sampler.generate_samples(
            self.config.parameter_ranges,
            n_samples=10000
        )

        # 2. 生成训练数据
        training_data = self.generate_training_data(parameter_sets)

        # 3. 训练神经网络
        self.neural_network = self.train_neural_network(training_data)

        # 4. 参数优化
        optimal_parameters = self.optimize_parameters()

        # 5. 计算置信区间
        confidence_intervals = self.calculate_confidence_intervals(
            optimal_parameters
        )

        return CalibrationResults(
            optimal_parameters=optimal_parameters,
            confidence_intervals=confidence_intervals,
            model=self.neural_network,
            training_history=self.neural_network.training_history
        )

    def train_neural_network(self, training_data: TrainingData) -> NeuralNetwork:
        """训练深度神经网络"""

        # 网络架构
        model = tf.keras.Sequential([
            tf.keras.layers.Dense(128, activation='relu',
                                input_shape=(len(self.config.parameter_ranges),)),
            tf.keras.layers.Dropout(0.2),
            tf.keras.layers.Dense(64, activation='relu'),
            tf.keras.layers.Dropout(0.2),
            tf.keras.layers.Dense(32, activation='relu'),
            tf.keras.layers.Dense(len(self.calibration_targets), activation='linear')
        ])

        # 编译模型
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
            loss='mse',
            metrics=['mae']
        )

        # 训练配置
        callbacks = [
            tf.keras.callbacks.EarlyStopping(patience=50, restore_best_weights=True),
            tf.keras.callbacks.ReduceLROnPlateau(factor=0.5, patience=20),
            CalibrationProgressCallback(self.report_training_progress)
        ]

        # 训练模型
        history = model.fit(
            training_data.X, training_data.y,
            validation_split=0.2,
            epochs=500,
            batch_size=32,
            callbacks=callbacks,
            verbose=0
        )

        return NeuralNetwork(model, history)

## 系统集成架构

### 服务层架构

#### 核心服务接口

```python
class SimulationService:
    """模拟服务层"""

    def __init__(self, db_manager: DatabaseManager,
                 config_manager: ConfigurationManager):
        self.db = db_manager
        self.config = config_manager
        self.engine = None
        self.current_simulation = None

    async def create_simulation(self, config: SimulationConfig) -> str:
        """创建新模拟"""
        simulation_id = str(uuid.uuid4())

        # 验证配置
        validation_result = self.validate_configuration(config)
        if not validation_result.is_valid:
            raise ValidationError(validation_result.errors)

        # 保存配置到数据库
        await self.db.save_simulation_config(simulation_id, config)

        return simulation_id

    async def start_simulation(self, simulation_id: str) -> None:
        """启动模拟执行"""
        config = await self.db.load_simulation_config(simulation_id)

        # 创建模拟引擎
        self.engine = MicrosimulationEngine(config)
        self.current_simulation = simulation_id

        # 在后台线程中运行模拟
        self.simulation_thread = SimulationThread(
            self.engine, simulation_id, self.on_progress_update
        )
        self.simulation_thread.start()

    def on_progress_update(self, progress_data: Dict):
        """处理进度更新"""
        # 更新数据库中的进度信息
        self.db.update_simulation_progress(
            self.current_simulation, progress_data
        )

        # 发送进度事件
        self.emit_progress_event(progress_data)

class DataService:
    """数据管理服务"""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.validators = DataValidatorFactory()

    async def import_life_table(self, file_path: str,
                               metadata: Dict) -> ImportResult:
        """导入生命表数据"""
        try:
            # 读取文件
            data = self.read_data_file(file_path)

            # 验证数据格式
            validator = self.validators.get_life_table_validator()
            validation_result = validator.validate(data)

            if not validation_result.is_valid:
                return ImportResult(
                    success=False,
                    errors=validation_result.errors
                )

            # 数据清洗和转换
            cleaned_data = self.clean_life_table_data(data)

            # 保存到数据库
            life_table_id = await self.db.save_life_table(
                cleaned_data, metadata
            )

            return ImportResult(
                success=True,
                id=life_table_id,
                records_imported=len(cleaned_data)
            )

        except Exception as e:
            return ImportResult(
                success=False,
                errors=[f"导入失败: {str(e)}"]
            )

    async def export_simulation_results(self, simulation_id: str,
                                      export_config: ExportConfig) -> str:
        """导出模拟结果"""
        results = await self.db.load_simulation_results(simulation_id)

        exporter = ExporterFactory.create_exporter(export_config.format)
        export_path = exporter.export(results, export_config)

        return export_path
```

#### 事件驱动架构

```python
class EventBus:
    """事件总线"""

    def __init__(self):
        self.subscribers = defaultdict(list)
        self.event_queue = asyncio.Queue()
        self.running = False

    def subscribe(self, event_type: str, handler: Callable):
        """订阅事件"""
        self.subscribers[event_type].append(handler)

    def unsubscribe(self, event_type: str, handler: Callable):
        """取消订阅"""
        if handler in self.subscribers[event_type]:
            self.subscribers[event_type].remove(handler)

    async def publish(self, event: Event):
        """发布事件"""
        await self.event_queue.put(event)

    async def start_processing(self):
        """开始处理事件"""
        self.running = True
        while self.running:
            try:
                event = await asyncio.wait_for(
                    self.event_queue.get(), timeout=1.0
                )
                await self.process_event(event)
            except asyncio.TimeoutError:
                continue

    async def process_event(self, event: Event):
        """处理单个事件"""
        handlers = self.subscribers.get(event.type, [])

        for handler in handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(event)
                else:
                    handler(event)
            except Exception as e:
                print(f"事件处理错误: {e}")

# 事件类型定义
class SimulationProgressEvent(Event):
    def __init__(self, simulation_id: str, progress: float,
                 current_year: int, memory_usage: float):
        super().__init__("simulation_progress")
        self.simulation_id = simulation_id
        self.progress = progress
        self.current_year = current_year
        self.memory_usage = memory_usage

class DataImportEvent(Event):
    def __init__(self, data_type: str, status: str,
                 records_count: int = 0, errors: List[str] = None):
        super().__init__("data_import")
        self.data_type = data_type
        self.status = status
        self.records_count = records_count
        self.errors = errors or []
```

### 性能优化架构

#### 内存管理

```python
class MemoryManager:
    """内存管理器"""

    def __init__(self, max_memory_gb: float = 8.0):
        self.max_memory_bytes = max_memory_gb * 1024 * 1024 * 1024
        self.current_usage = 0
        self.memory_pools = {}
        self.gc_threshold = 0.8  # 80%时触发垃圾回收

    def allocate_individual_batch(self, batch_size: int) -> IndividualBatch:
        """分配个体批次内存"""
        estimated_size = batch_size * self.estimate_individual_size()

        if self.current_usage + estimated_size > self.max_memory_bytes:
            self.trigger_garbage_collection()

        if self.current_usage + estimated_size > self.max_memory_bytes:
            raise MemoryError("内存不足，无法分配个体批次")

        batch = IndividualBatch(batch_size)
        self.current_usage += estimated_size

        return batch

    def trigger_garbage_collection(self):
        """触发垃圾回收"""
        import gc

        # 强制垃圾回收
        collected = gc.collect()

        # 更新内存使用统计
        self.update_memory_usage()

        print(f"垃圾回收完成，回收对象数: {collected}")

    def get_memory_usage_stats(self) -> Dict:
        """获取内存使用统计"""
        import psutil

        process = psutil.Process()
        memory_info = process.memory_info()

        return {
            'rss_gb': memory_info.rss / 1024 / 1024 / 1024,
            'vms_gb': memory_info.vms / 1024 / 1024 / 1024,
            'percent': process.memory_percent(),
            'available_gb': psutil.virtual_memory().available / 1024 / 1024 / 1024
        }

class ParallelComputeManager:
    """并行计算管理器"""

    def __init__(self, max_processes: int = None):
        self.max_processes = max_processes or multiprocessing.cpu_count()
        self.process_pool = None
        self.task_queue = Queue()
        self.result_queue = Queue()

    def initialize_pool(self):
        """初始化进程池"""
        self.process_pool = multiprocessing.Pool(
            processes=self.max_processes,
            initializer=self.worker_init,
            initargs=(self.task_queue, self.result_queue)
        )

    def parallel_individual_processing(self, individuals: List[Individual],
                                     processing_func: Callable,
                                     batch_size: int = 1000) -> List[Any]:
        """并行处理个体数据"""

        # 分批处理
        batches = [individuals[i:i+batch_size]
                  for i in range(0, len(individuals), batch_size)]

        # 提交任务到进程池
        futures = []
        for batch in batches:
            future = self.process_pool.apply_async(
                processing_func, (batch,)
            )
            futures.append(future)

        # 收集结果
        results = []
        for future in futures:
            batch_result = future.get(timeout=300)  # 5分钟超时
            results.extend(batch_result)

        return results

    def cleanup(self):
        """清理资源"""
        if self.process_pool:
            self.process_pool.close()
            self.process_pool.join()
```

## 部署架构

### 桌面应用打包

#### PyInstaller配置

```python
# build_config.py
import PyInstaller.__main__

def build_application():
    """构建桌面应用程序"""

    PyInstaller.__main__.run([
        'src/interfaces/desktop/main.py',
        '--name=ColorectalScreeningModel',
        '--windowed',  # 无控制台窗口
        '--onefile',   # 单文件模式
        '--add-data=src/interfaces/desktop/resources;resources',
        '--add-data=data/default_configs;configs',
        '--hidden-import=tensorflow',
        '--hidden-import=sklearn',
        '--hidden-import=scipy',
        '--exclude-module=tkinter',  # 排除不需要的模块
        '--icon=resources/icons/app_icon.ico',
        '--version-file=version_info.txt',
        '--distpath=dist',
        '--workpath=build',
        '--specpath=build',
        '--clean',
        '--noconfirm'
    ])

# version_info.txt
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo([
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'医疗政策研究中心'),
         StringStruct(u'FileDescription', u'结直肠癌筛查微观模拟模型'),
         StringStruct(u'FileVersion', u'*******'),
         StringStruct(u'InternalName', u'ColorectalScreeningModel'),
         StringStruct(u'LegalCopyright', u'Copyright © 2025'),
         StringStruct(u'OriginalFilename', u'ColorectalScreeningModel.exe'),
         StringStruct(u'ProductName', u'结直肠癌筛查微观模拟模型'),
         StringStruct(u'ProductVersion', u'*******')])
    ]),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
```

#### 跨平台安装包

```yaml
# .github/workflows/build.yml
name: Build Desktop Application

on:
  push:
    tags:
      - 'v*'

jobs:
  build-windows:
    runs-on: windows-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.8'

    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pyinstaller

    - name: Build Windows executable
      run: python build_config.py

    - name: Create Windows installer
      run: |
        # 使用NSIS创建Windows安装程序
        makensis installer/windows/installer.nsi

    - name: Upload Windows artifacts
      uses: actions/upload-artifact@v3
      with:
        name: windows-installer
        path: dist/ColorectalScreeningModel-Setup.exe

  build-macos:
    runs-on: macos-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.8'

    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pyinstaller

    - name: Build macOS application
      run: |
        python build_config.py
        # 创建DMG安装包
        create-dmg \
          --volname "结直肠癌筛查模拟模型" \
          --window-pos 200 120 \
          --window-size 600 300 \
          --icon-size 100 \
          --app-drop-link 425 120 \
          "dist/ColorectalScreeningModel.dmg" \
          "dist/ColorectalScreeningModel.app"

  build-linux:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.8'

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y python3-pyqt6 python3-pyqt6.qtcharts

    - name: Install Python dependencies
      run: |
        pip install -r requirements.txt
        pip install pyinstaller

    - name: Build Linux application
      run: python build_config.py

    - name: Create AppImage
      run: |
        # 使用linuxdeploy创建AppImage
        wget https://github.com/linuxdeploy/linuxdeploy/releases/download/continuous/linuxdeploy-x86_64.AppImage
        chmod +x linuxdeploy-x86_64.AppImage
        ./linuxdeploy-x86_64.AppImage --appdir AppDir --executable dist/ColorectalScreeningModel --desktop-file installer/linux/colorectal-screening-model.desktop --icon-file resources/icons/app_icon.png --output appimage

## 统一项目结构

### 完整目录结构

```
colorectal-screening-model/
├── README.md
├── requirements.txt
├── pyproject.toml
├── setup.py
├── .gitignore
├── .github/
│   └── workflows/
│       ├── build.yml
│       ├── test.yml
│       └── release.yml
├── docs/
│   ├── prd.md
│   ├── architecture.md
│   ├── front-end-spec.md
│   ├── api/
│   ├── user-guide/
│   └── developer-guide/
├── src/
│   ├── __init__.py
│   ├── core/                          # 核心模拟引擎
│   │   ├── __init__.py
│   │   ├── engine.py                  # 主模拟引擎
│   │   ├── population.py              # 人群管理
│   │   ├── individual.py              # 个体类
│   │   └── simulation_config.py       # 配置管理
│   ├── modules/                       # 功能模块
│   │   ├── __init__.py
│   │   ├── disease/                   # 疾病建模
│   │   │   ├── __init__.py
│   │   │   ├── disease_model.py
│   │   │   ├── adenoma_pathway.py
│   │   │   ├── serrated_pathway.py
│   │   │   └── risk_factors.py
│   │   ├── screening/                 # 筛查模拟
│   │   │   ├── __init__.py
│   │   │   ├── screening_model.py
│   │   │   ├── screening_tools.py
│   │   │   ├── strategies.py
│   │   │   └── compliance.py
│   │   └── economics/                 # 经济分析
│   │       ├── __init__.py
│   │       ├── economic_model.py
│   │       ├── cost_calculator.py
│   │       ├── qaly_calculator.py
│   │       └── icer_analysis.py
│   ├── calibration/                   # 机器学习校准
│   │   ├── __init__.py
│   │   ├── ml_engine.py
│   │   ├── parameter_sampler.py
│   │   ├── neural_network.py
│   │   ├── optimization.py
│   │   └── confidence_intervals.py
│   ├── interfaces/                    # 用户界面
│   │   ├── __init__.py
│   │   ├── desktop/                   # 桌面应用
│   │   │   ├── main.py
│   │   │   ├── app/
│   │   │   ├── windows/
│   │   │   ├── widgets/
│   │   │   ├── models/
│   │   │   ├── services/
│   │   │   ├── resources/
│   │   │   └── utils/
│   │   └── cli/                       # 命令行界面
│   │       ├── __init__.py
│   │       ├── main.py
│   │       ├── commands/
│   │       └── utils/
│   ├── data/                          # 数据管理
│   │   ├── __init__.py
│   │   ├── database/
│   │   │   ├── __init__.py
│   │   │   ├── models.py
│   │   │   ├── migrations/
│   │   │   └── repositories/
│   │   ├── importers/
│   │   │   ├── __init__.py
│   │   │   ├── life_table_importer.py
│   │   │   ├── calibration_importer.py
│   │   │   └── base_importer.py
│   │   ├── exporters/
│   │   │   ├── __init__.py
│   │   │   ├── csv_exporter.py
│   │   │   ├── excel_exporter.py
│   │   │   └── json_exporter.py
│   │   └── validators/
│   │       ├── __init__.py
│   │       ├── data_validator.py
│   │       └── schema_validator.py
│   └── utils/                         # 工具函数
│       ├── __init__.py
│       ├── performance.py
│       ├── memory_manager.py
│       ├── parallel_compute.py
│       ├── logging_config.py
│       └── helpers.py
├── data/                              # 数据文件
│   ├── life_tables/
│   │   ├── china_2020.csv
│   │   └── regional/
│   ├── calibration_targets/
│   │   ├── adenoma_prevalence.json
│   │   ├── cancer_incidence.json
│   │   └── mortality_rates.json
│   ├── screening_parameters/
│   │   ├── fit_parameters.yaml
│   │   ├── colonoscopy_parameters.yaml
│   │   └── sigmoidoscopy_parameters.yaml
│   └── default_configs/
│       ├── simulation_config.yaml
│       ├── disease_config.yaml
│       └── economic_config.yaml
├── tests/                             # 测试文件
│   ├── __init__.py
│   ├── unit/
│   │   ├── test_core/
│   │   ├── test_modules/
│   │   ├── test_calibration/
│   │   └── test_interfaces/
│   ├── integration/
│   │   ├── test_simulation_flow.py
│   │   ├── test_data_pipeline.py
│   │   └── test_calibration_flow.py
│   ├── performance/
│   │   ├── test_large_population.py
│   │   ├── test_memory_usage.py
│   │   └── test_parallel_compute.py
│   └── fixtures/
│       ├── sample_data/
│       └── test_configs/
├── examples/                          # 示例和教程
│   ├── basic_simulation.py
│   ├── calibration_example.py
│   ├── economic_analysis.py
│   └── notebooks/
│       ├── getting_started.ipynb
│       ├── advanced_calibration.ipynb
│       └── result_analysis.ipynb
├── scripts/                           # 构建和部署脚本
│   ├── build_config.py
│   ├── setup_dev_env.py
│   ├── run_tests.py
│   └── deploy/
│       ├── build_windows.bat
│       ├── build_macos.sh
│       └── build_linux.sh
├── installer/                         # 安装程序配置
│   ├── windows/
│   │   ├── installer.nsi
│   │   └── app_icon.ico
│   ├── macos/
│   │   ├── Info.plist
│   │   └── app_icon.icns
│   └── linux/
│       ├── colorectal-screening-model.desktop
│       └── app_icon.png
└── dist/                             # 构建输出目录
    ├── ColorectalScreeningModel.exe  # Windows可执行文件
    ├── ColorectalScreeningModel.app  # macOS应用包
    └── ColorectalScreeningModel.AppImage # Linux AppImage
```

### 配置文件管理

#### 主配置文件 (pyproject.toml)

```toml
[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "colorectal-screening-model"
version = "1.0.0"
description = "结直肠癌筛查微观模拟模型"
authors = [
    {name = "医疗政策研究中心", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.8"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Healthcare Industry",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Topic :: Scientific/Engineering :: Medical Science Apps.",
]

dependencies = [
    "numpy>=1.24.0",
    "pandas>=2.0.0",
    "scipy>=1.10.0",
    "scikit-learn>=1.3.0",
    "tensorflow>=2.13.0",
    "matplotlib>=3.7.0",
    "plotly>=5.15.0",
    "PyQt6>=6.5.0",
    "SQLAlchemy>=2.0.0",
    "PyYAML>=6.0",
    "psutil>=5.9.0",
    "tqdm>=4.65.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "mypy>=1.5.0",
    "flake8>=6.0.0",
    "sphinx>=7.0.0",
    "sphinx-rtd-theme>=1.3.0",
]

build = [
    "pyinstaller>=5.13.0",
    "setuptools>=61.0",
    "wheel>=0.40.0",
]

[project.scripts]
colorectal-sim = "src.interfaces.cli.main:main"

[project.gui-scripts]
colorectal-sim-gui = "src.interfaces.desktop.main:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=src",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "performance: marks tests as performance tests",
]
```

## 开发指南

### 开发环境设置

#### 环境准备

```bash
# 1. 克隆仓库
git clone https://github.com/medical-policy/colorectal-screening-model.git
cd colorectal-screening-model

# 2. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate     # Windows

# 3. 安装依赖
pip install -e ".[dev]"

# 4. 设置预提交钩子
pre-commit install

# 5. 运行测试确保环境正常
pytest tests/unit/
```

#### IDE配置

```json
// .vscode/settings.json
{
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "python.formatting.blackArgs": ["--line-length=88"],
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": ["tests"],
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        ".pytest_cache": true,
        ".coverage": true,
        "htmlcov": true
    }
}
```

### 编码规范

#### Python代码规范

```python
# 示例：符合项目规范的代码结构

from typing import Dict, List, Optional, Union
from dataclasses import dataclass
from abc import ABC, abstractmethod
import logging

logger = logging.getLogger(__name__)

@dataclass
class SimulationParameters:
    """模拟参数数据类

    Attributes:
        population_size: 人群规模
        duration_years: 模拟年数
        time_step_months: 时间步长（月）
    """
    population_size: int
    duration_years: int = 100
    time_step_months: int = 3

    def __post_init__(self):
        """参数验证"""
        if self.population_size <= 0:
            raise ValueError("人群规模必须大于0")
        if self.duration_years <= 0:
            raise ValueError("模拟年数必须大于0")

class BaseSimulationModule(ABC):
    """模拟模块基类"""

    def __init__(self, config: Dict):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)

    @abstractmethod
    def initialize(self) -> None:
        """初始化模块"""
        pass

    @abstractmethod
    def process(self, data: Any) -> Any:
        """处理数据"""
        pass

    def validate_config(self) -> bool:
        """验证配置"""
        required_keys = self.get_required_config_keys()
        return all(key in self.config for key in required_keys)

    @abstractmethod
    def get_required_config_keys(self) -> List[str]:
        """获取必需的配置键"""
        pass

# 错误处理示例
class SimulationError(Exception):
    """模拟相关错误基类"""
    pass

class ConfigurationError(SimulationError):
    """配置错误"""
    pass

class DataValidationError(SimulationError):
    """数据验证错误"""
    pass

# 使用示例
def run_simulation_safely(config: SimulationParameters) -> Optional[SimulationResults]:
    """安全运行模拟"""
    try:
        engine = MicrosimulationEngine(config)
        results = engine.run()
        logger.info(f"模拟完成，处理了 {config.population_size} 个个体")
        return results

    except ConfigurationError as e:
        logger.error(f"配置错误: {e}")
        return None

    except DataValidationError as e:
        logger.error(f"数据验证失败: {e}")
        return None

    except Exception as e:
        logger.exception(f"模拟运行失败: {e}")
        return None
```

### 测试策略

#### 测试层次结构

```python
# tests/unit/test_disease_model.py
import pytest
from unittest.mock import Mock, patch
from src.modules.disease.disease_model import DiseaseModel, AdenomaCarcinomaPathway

class TestDiseaseModel:
    """疾病模型单元测试"""

    @pytest.fixture
    def disease_config(self):
        """测试配置"""
        return {
            'adenoma_config': {
                'base_transition_rates': {
                    'normal_to_low_risk': 0.001,
                    'low_risk_to_high_risk': 0.01,
                },
                'gender_multipliers': {'male': 1.2, 'female': 1.0}
            }
        }

    @pytest.fixture
    def disease_model(self, disease_config):
        """疾病模型实例"""
        return DiseaseModel(disease_config)

    def test_pathway_selection(self, disease_model):
        """测试疾病通路选择"""
        individual = Mock()
        individual.pathway_type = 'adenoma_carcinoma'

        pathway = disease_model.get_pathway(individual)
        assert isinstance(pathway, AdenomaCarcinomaPathway)

    def test_transition_probability_calculation(self, disease_model):
        """测试转换概率计算"""
        individual = Mock()
        individual.age = 55
        individual.gender = 'male'
        individual.current_disease_state = 'normal'

        prob = disease_model.calculate_transition_probability(individual, 0)
        assert 0 <= prob <= 1

    @pytest.mark.slow
    def test_large_population_processing(self, disease_model):
        """测试大人群处理性能"""
        individuals = [Mock() for _ in range(10000)]

        start_time = time.time()
        disease_model.process_population(individuals)
        end_time = time.time()

        assert end_time - start_time < 30  # 30秒内完成

# tests/integration/test_simulation_flow.py
class TestSimulationFlow:
    """模拟流程集成测试"""

    def test_complete_simulation_flow(self):
        """测试完整模拟流程"""
        config = SimulationConfig.load_from_file('tests/fixtures/test_config.yaml')

        engine = MicrosimulationEngine(config)
        results = engine.run()

        assert results is not None
        assert results.population_outcomes is not None
        assert results.economic_outcomes is not None

    def test_data_import_export_flow(self):
        """测试数据导入导出流程"""
        # 导入测试数据
        importer = LifeTableImporter()
        life_table = importer.import_from_file('tests/fixtures/test_life_table.csv')

        # 运行模拟
        config = SimulationConfig(life_table=life_table)
        engine = MicrosimulationEngine(config)
        results = engine.run()

        # 导出结果
        exporter = CSVExporter()
        export_path = exporter.export(results, 'test_output.csv')

        assert os.path.exists(export_path)
```

### 性能监控

#### 性能测试框架

```python
# tests/performance/performance_test_base.py
import time
import psutil
import pytest
from typing import Dict, Any

class PerformanceTestBase:
    """性能测试基类"""

    def __init__(self):
        self.start_time = None
        self.start_memory = None
        self.process = psutil.Process()

    def start_monitoring(self):
        """开始性能监控"""
        self.start_time = time.time()
        self.start_memory = self.process.memory_info().rss

    def stop_monitoring(self) -> Dict[str, Any]:
        """停止监控并返回性能指标"""
        end_time = time.time()
        end_memory = self.process.memory_info().rss

        return {
            'execution_time': end_time - self.start_time,
            'memory_usage_mb': (end_memory - self.start_memory) / 1024 / 1024,
            'peak_memory_mb': self.process.memory_info().rss / 1024 / 1024,
            'cpu_percent': self.process.cpu_percent()
        }

    def assert_performance_requirements(self, metrics: Dict[str, Any],
                                      max_time: float = None,
                                      max_memory_mb: float = None):
        """断言性能要求"""
        if max_time and metrics['execution_time'] > max_time:
            pytest.fail(f"执行时间 {metrics['execution_time']:.2f}s 超过限制 {max_time}s")

        if max_memory_mb and metrics['memory_usage_mb'] > max_memory_mb:
            pytest.fail(f"内存使用 {metrics['memory_usage_mb']:.2f}MB 超过限制 {max_memory_mb}MB")

# tests/performance/test_large_scale_simulation.py
class TestLargeScaleSimulation(PerformanceTestBase):
    """大规模模拟性能测试"""

    @pytest.mark.performance
    def test_million_individual_simulation(self):
        """测试100万个体模拟性能"""
        config = SimulationConfig(
            population_size=1000000,
            duration_years=10,  # 缩短测试时间
            parallel_enabled=True
        )

        self.start_monitoring()

        engine = MicrosimulationEngine(config)
        results = engine.run()

        metrics = self.stop_monitoring()

        # 性能要求：30分钟内完成，内存使用不超过8GB
        self.assert_performance_requirements(
            metrics,
            max_time=1800,  # 30分钟
            max_memory_mb=8192  # 8GB
        )

        assert results is not None
        assert len(results.individual_outcomes) == 1000000
```

## 总结

本架构文档定义了结直肠癌筛查微观模拟模型的完整全栈架构，包括：

### 关键架构特点

1. **模块化设计**: 支持疾病模块、筛查工具和经济评估器的灵活扩展
2. **高性能计算**: 支持100万个体、100年周期的大规模模拟
3. **机器学习集成**: 基于深度神经网络的智能参数校准
4. **专业用户体验**: 科学研究级别的桌面应用界面
5. **跨平台兼容**: 支持Windows、macOS、Linux操作系统

### 技术栈优势

- **Python生态系统**: 丰富的科学计算和机器学习库支持
- **PyQt6桌面框架**: 原生性能和专业界面组件
- **SQLite数据库**: 零配置的嵌入式数据存储
- **并行计算支持**: 充分利用多核CPU资源
- **内存优化管理**: 支持大规模数据处理

### 开发和部署

- **标准化开发流程**: 完整的测试、构建、部署流水线
- **跨平台打包**: 自动化的多平台安装包生成
- **性能监控**: 全面的性能测试和监控框架
- **代码质量保证**: 严格的编码规范和质量检查

该架构为医疗政策研究人员提供了一个强大、可靠、易用的结直肠癌筛查策略分析工具，支持循证医学决策和政策制定。
```
```
```
```
